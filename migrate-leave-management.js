#!/usr/bin/env node

/**
 * Automated Leave Management Migration Script
 * Migrates complete leave management system from kawandamahrsystem to TCM Enterprise Suite
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  SOURCE_PROJECT: '/Users/<USER>/Documents/REACT BLOG APP/KAWANDAMA PROJECTS/kawandamahrsystem',
  TARGET_PROJECT: '/Users/<USER>/Documents/REACT BLOG APP/lasttcmsuit',
  BACKUP_DIR: './migration-backup',
  DRY_RUN: false // Set to true to preview changes without executing
};

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Logging utility
const log = {
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✓ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}✗ ${msg}${colors.reset}`),
  step: (msg) => console.log(`${colors.cyan}${colors.bright}▶ ${msg}${colors.reset}`),
  substep: (msg) => console.log(`  ${colors.magenta}• ${msg}${colors.reset}`)
};

// File and directory operations
class FileOperations {
  static ensureDir(dirPath) {
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      log.substep(`Created directory: ${dirPath}`);
    }
  }

  static copyFile(source, target) {
    if (CONFIG.DRY_RUN) {
      log.substep(`[DRY RUN] Would copy: ${source} → ${target}`);
      return;
    }
    
    this.ensureDir(path.dirname(target));
    fs.copyFileSync(source, target);
    log.substep(`Copied: ${path.basename(source)}`);
  }

  static copyDirectory(source, target, options = {}) {
    if (!fs.existsSync(source)) {
      log.warning(`Source directory not found: ${source}`);
      return;
    }

    if (CONFIG.DRY_RUN) {
      log.substep(`[DRY RUN] Would copy directory: ${source} → ${target}`);
      return;
    }

    this.ensureDir(target);
    
    const items = fs.readdirSync(source);
    items.forEach(item => {
      const sourcePath = path.join(source, item);
      const targetPath = path.join(target, item);
      
      if (options.exclude && options.exclude.some(pattern => item.includes(pattern))) {
        return;
      }
      
      const stat = fs.statSync(sourcePath);
      if (stat.isDirectory()) {
        this.copyDirectory(sourcePath, targetPath, options);
      } else {
        fs.copyFileSync(sourcePath, targetPath);
      }
    });
    
    log.substep(`Copied directory: ${path.basename(source)}`);
  }

  static updateFileContent(filePath, replacements) {
    if (!fs.existsSync(filePath)) {
      log.warning(`File not found for update: ${filePath}`);
      return;
    }

    if (CONFIG.DRY_RUN) {
      log.substep(`[DRY RUN] Would update: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let updated = false;

    replacements.forEach(({ from, to, description }) => {
      if (content.includes(from)) {
        content = content.replace(new RegExp(from, 'g'), to);
        updated = true;
        log.substep(`Updated ${description} in ${path.basename(filePath)}`);
      }
    });

    if (updated) {
      fs.writeFileSync(filePath, content);
    }
  }

  static createBackup() {
    if (CONFIG.DRY_RUN) {
      log.substep('[DRY RUN] Would create backup');
      return;
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${CONFIG.BACKUP_DIR}-${timestamp}`;
    
    log.substep('Creating backup of target project...');
    execSync(`cp -r "${CONFIG.TARGET_PROJECT}" "${backupPath}"`);
    log.success(`Backup created: ${backupPath}`);
  }
}

// Migration tasks
class MigrationTasks {
  static async migrateModels() {
    log.step('Phase 1: Migrating Database Models');
    
    const sourceModels = path.join(CONFIG.SOURCE_PROJECT, 'models/leave');
    const targetModels = path.join(CONFIG.TARGET_PROJECT, 'models/leave');
    
    FileOperations.copyDirectory(sourceModels, targetModels);
    
    // Update import paths in models
    const modelFiles = [
      'Leave.ts', 'LeaveType.ts', 'LeaveBalance.ts', 'LeaveAccrual.ts',
      'LeaveAccrualRule.ts', 'LeaveApprovalWorkflow.ts', 'LeaveApprovalTemplate.ts',
      'LeaveEncashment.ts', 'LeaveEncashmentRule.ts', 'LeaveSettings.ts'
    ];
    
    modelFiles.forEach(file => {
      const filePath = path.join(targetModels, file);
      FileOperations.updateFileContent(filePath, [
        {
          from: '@/lib/backend/database',
          to: '@/lib/database',
          description: 'database import path'
        },
        {
          from: '@/models/Employee',
          to: '@/models/Employee',
          description: 'Employee model import'
        }
      ]);
    });
    
    log.success('Models migration completed');
  }

  static async migrateServices() {
    log.step('Phase 2: Migrating Backend Services');
    
    const sourceServices = path.join(CONFIG.SOURCE_PROJECT, 'services/leave');
    const targetServices = path.join(CONFIG.TARGET_PROJECT, 'services/leave');
    
    FileOperations.copyDirectory(sourceServices, targetServices);
    
    // Update service imports
    const serviceFiles = [
      'LeaveService.ts', 'LeaveAccrualService.ts', 'LeaveApprovalWorkflowService.ts',
      'LeaveEncashmentService.ts', 'LeaveReportingService.ts'
    ];
    
    serviceFiles.forEach(file => {
      const filePath = path.join(targetServices, file);
      FileOperations.updateFileContent(filePath, [
        {
          from: '@/lib/backend/database',
          to: '@/lib/database',
          description: 'database import path'
        },
        {
          from: '@/lib/backend/auth/auth',
          to: '@/lib/auth',
          description: 'auth import path'
        },
        {
          from: '@/lib/backend/utils/logger',
          to: '@/lib/logger',
          description: 'logger import path'
        }
      ]);
    });
    
    log.success('Services migration completed');
  }

  static async migrateAPIRoutes() {
    log.step('Phase 3: Migrating API Routes');
    
    // Copy main leave API routes
    const sourceAPI = path.join(CONFIG.SOURCE_PROJECT, 'app/api/leave');
    const targetAPI = path.join(CONFIG.TARGET_PROJECT, 'app/api/leave');
    
    FileOperations.copyDirectory(sourceAPI, targetAPI, {
      exclude: ['.corrupted', '.bak']
    });
    
    // Copy integration API routes
    const integrationRoutes = [
      { source: 'app/api/dashboard/leave/route.ts', target: 'app/api/dashboard/leave/route.ts' },
      { source: 'app/api/hr/leave/route.ts', target: 'app/api/hr/leave/route.ts' },
      { source: 'app/api/payroll/leave-data/route.ts', target: 'app/api/payroll/leave-data/route.ts' },
      { source: 'app/api/admin/seed/leave-types/route.ts', target: 'app/api/admin/seed/leave-types/route.ts' }
    ];
    
    integrationRoutes.forEach(({ source, target }) => {
      const sourcePath = path.join(CONFIG.SOURCE_PROJECT, source);
      const targetPath = path.join(CONFIG.TARGET_PROJECT, target);
      FileOperations.copyFile(sourcePath, targetPath);
    });
    
    // Update API route imports
    this.updateAPIImports(targetAPI);
    
    log.success('API routes migration completed');
  }

  static updateAPIImports(apiDir) {
    const updateImportsInDir = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const items = fs.readdirSync(dir);
      items.forEach(item => {
        const itemPath = path.join(dir, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          updateImportsInDir(itemPath);
        } else if (item.endsWith('.ts')) {
          FileOperations.updateFileContent(itemPath, [
            {
              from: '@/lib/backend/auth/auth',
              to: '@/lib/auth',
              description: 'auth import'
            },
            {
              from: '@/lib/backend/database',
              to: '@/lib/database',
              description: 'database import'
            },
            {
              from: '@/lib/backend/utils/logger',
              to: '@/lib/logger',
              description: 'logger import'
            },
            {
              from: '@/lib/backend/utils/permissions',
              to: '@/lib/permissions',
              description: 'permissions import'
            }
          ]);
        }
      });
    };
    
    updateImportsInDir(apiDir);
  }

  static async migrateFrontendComponents() {
    log.step('Phase 4: Migrating Frontend Components');
    
    // Copy main leave management components
    const sourceComponents = path.join(CONFIG.SOURCE_PROJECT, 'components/leave-management');
    const targetComponents = path.join(CONFIG.TARGET_PROJECT, 'components/leave-management');
    
    FileOperations.copyDirectory(sourceComponents, targetComponents);
    
    // Copy form components
    const formComponents = [
      { source: 'components/forms/leave-request-form.tsx', target: 'components/forms/leave-request-form.tsx' },
      { source: 'components/forms/overlays/leave-request-form-overlay.tsx', target: 'components/forms/overlays/leave-request-form-overlay.tsx' }
    ];
    
    formComponents.forEach(({ source, target }) => {
      const sourcePath = path.join(CONFIG.SOURCE_PROJECT, source);
      const targetPath = path.join(CONFIG.TARGET_PROJECT, target);
      FileOperations.copyFile(sourcePath, targetPath);
    });
    
    // Copy leave types components
    const sourceLeaveTypes = path.join(CONFIG.SOURCE_PROJECT, 'components/leave-types');
    const targetLeaveTypes = path.join(CONFIG.TARGET_PROJECT, 'components/leave-types');
    
    FileOperations.copyDirectory(sourceLeaveTypes, targetLeaveTypes);
    
    // Copy additional components
    const additionalComponents = [
      { source: 'components/leave-management.tsx', target: 'components/leave-management.tsx' },
      { source: 'components/leave/MobileLeaveRequestForm.tsx', target: 'components/leave/MobileLeaveRequestForm.tsx' },
      { source: 'components/hr/leave/leave-management-page.tsx', target: 'components/hr/leave/leave-management-page.tsx' }
    ];
    
    additionalComponents.forEach(({ source, target }) => {
      const sourcePath = path.join(CONFIG.SOURCE_PROJECT, source);
      const targetPath = path.join(CONFIG.TARGET_PROJECT, target);
      FileOperations.copyFile(sourcePath, targetPath);
    });
    
    log.success('Frontend components migration completed');
  }

  static async migrateHooksAndTypes() {
    log.step('Phase 5: Migrating Hooks and Types');
    
    // Copy hooks
    const hookFiles = [
      'use-leave-requests.ts', 'use-leave-types.ts', 'use-leave-balances.ts',
      'use-leave-settings.ts', 'use-leave-accruals.ts', 'use-leave-reports.ts',
      'use-leave-encashments.ts', 'use-employee-leave-balances.ts',
      'use-department-leave-summaries.ts', 'use-all-employees-leave-balances.ts'
    ];
    
    hookFiles.forEach(file => {
      const sourcePath = path.join(CONFIG.SOURCE_PROJECT, 'hooks', file);
      const targetPath = path.join(CONFIG.TARGET_PROJECT, 'hooks', file);
      FileOperations.copyFile(sourcePath, targetPath);
    });
    
    // Copy types
    const sourcePath = path.join(CONFIG.SOURCE_PROJECT, 'types/leave-request.ts');
    const targetPath = path.join(CONFIG.TARGET_PROJECT, 'types/leave-request.ts');
    FileOperations.copyFile(sourcePath, targetPath);
    
    log.success('Hooks and types migration completed');
  }

  static async migrateDashboardPages() {
    log.step('Phase 6: Migrating Dashboard Pages');
    
    // Copy leave dashboard pages
    const sourcePages = path.join(CONFIG.SOURCE_PROJECT, 'app/(dashboard)/dashboard/leave');
    const targetPages = path.join(CONFIG.TARGET_PROJECT, 'app/(dashboard)/dashboard/leave');
    
    FileOperations.copyDirectory(sourcePages, targetPages);
    
    // Copy HR leave page
    const sourceHRPage = path.join(CONFIG.SOURCE_PROJECT, 'app/(dashboard)/dashboard/hr/leave/page.tsx');
    const targetHRPage = path.join(CONFIG.TARGET_PROJECT, 'app/(dashboard)/dashboard/hr/leave/page.tsx');
    FileOperations.copyFile(sourceHRPage, targetHRPage);
    
    // Update all pages to use DashboardShell
    this.wrapPagesInDashboardShell(targetPages);
    
    log.success('Dashboard pages migration completed');
  }

  static wrapPagesInDashboardShell(pagesDir) {
    const wrapPageInShell = (filePath) => {
      if (!filePath.endsWith('page.tsx')) return;

      FileOperations.updateFileContent(filePath, [
        {
          from: 'export default function',
          to: 'import { DashboardShell } from "@/components/dashboard-shell"\n\nexport default function',
          description: 'DashboardShell import'
        }
      ]);

      // Add DashboardShell wrapper (this is a simplified approach)
      let content = fs.readFileSync(filePath, 'utf8');
      if (!content.includes('DashboardShell') && !CONFIG.DRY_RUN) {
        // This would need more sophisticated parsing for proper wrapping
        log.substep(`Manual review needed for DashboardShell wrapping: ${filePath}`);
      }
    };

    const processDir = (dir) => {
      if (!fs.existsSync(dir)) return;

      const items = fs.readdirSync(dir);
      items.forEach(item => {
        const itemPath = path.join(dir, item);
        const stat = fs.statSync(itemPath);

        if (stat.isDirectory()) {
          processDir(itemPath);
        } else if (item === 'page.tsx') {
          wrapPageInShell(itemPath);
        }
      });
    };

    processDir(pagesDir);
  }

  static async migrateSupportingFiles() {
    log.step('Phase 7: Migrating Supporting Files');

    // Copy seeders
    const sourceSeeder = path.join(CONFIG.SOURCE_PROJECT, 'lib/backend/seeders/leave-types-seeder.ts');
    const targetSeeder = path.join(CONFIG.TARGET_PROJECT, 'lib/seeders/leave-types-seeder.ts');
    FileOperations.copyFile(sourceSeeder, targetSeeder);

    // Copy dashboard service
    const sourceDashboardService = path.join(CONFIG.SOURCE_PROJECT, 'lib/services/dashboard/leave-dashboard-service.ts');
    const targetDashboardService = path.join(CONFIG.TARGET_PROJECT, 'lib/services/dashboard/leave-dashboard-service.ts');
    FileOperations.copyFile(sourceDashboardService, targetDashboardService);

    // Copy debug components (optional)
    const sourceDebug = path.join(CONFIG.SOURCE_PROJECT, 'components/debug-services');
    const targetDebug = path.join(CONFIG.TARGET_PROJECT, 'components/debug-services');

    const debugFiles = [
      'leave-debug-page.tsx',
      'leave-dashboard-debug.tsx',
      'leave-requests-debug.tsx'
    ];

    debugFiles.forEach(file => {
      const sourcePath = path.join(sourceDebug, file);
      const targetPath = path.join(targetDebug, file);
      if (fs.existsSync(sourcePath)) {
        FileOperations.copyFile(sourcePath, targetPath);
      }
    });

    log.success('Supporting files migration completed');
  }

  static async updatePayrollIntegration() {
    log.step('Phase 8: Updating Payroll Integration');

    // Update payroll service to include leave data
    const payrollServicePath = path.join(CONFIG.TARGET_PROJECT, 'services/payroll/PayrollService.ts');

    if (fs.existsSync(payrollServicePath)) {
      FileOperations.updateFileContent(payrollServicePath, [
        {
          from: 'import { connectToDatabase }',
          to: 'import { connectToDatabase }\nimport { LeaveService } from \'@/services/leave/LeaveService\'',
          description: 'LeaveService import'
        }
      ]);

      log.substep('Updated PayrollService with leave integration');
    } else {
      log.warning('PayrollService not found - manual integration required');
    }

    // Update salary calculation to include leave deductions
    const salaryServicePath = path.join(CONFIG.TARGET_PROJECT, 'services/payroll/SalaryService.ts');

    if (fs.existsSync(salaryServicePath)) {
      log.substep('SalaryService found - manual leave deduction integration required');
    }

    log.success('Payroll integration updates completed');
  }

  static async updateNavigation() {
    log.step('Phase 9: Updating Navigation');

    // Find navigation configuration file
    const possibleNavFiles = [
      'components/sidebar-nav.tsx',
      'components/navigation.tsx',
      'lib/navigation.ts',
      'config/navigation.ts'
    ];

    let navFile = null;
    for (const file of possibleNavFiles) {
      const filePath = path.join(CONFIG.TARGET_PROJECT, file);
      if (fs.existsSync(filePath)) {
        navFile = filePath;
        break;
      }
    }

    if (navFile) {
      log.substep(`Found navigation file: ${navFile}`);
      log.substep('Manual navigation update required - add leave management menu items');
    } else {
      log.warning('Navigation file not found - manual navigation setup required');
    }

    log.success('Navigation update guidance provided');
  }

  static async createPostMigrationTasks() {
    log.step('Phase 10: Creating Post-Migration Tasks');

    const tasksFile = path.join(CONFIG.TARGET_PROJECT, 'POST_MIGRATION_TASKS.md');

    const tasks = `# Post-Migration Tasks for Leave Management System

## Immediate Tasks (Required)

### 1. Update Navigation
Add leave management menu items to your navigation configuration:
\`\`\`typescript
{
  title: "Leave Management",
  href: "/dashboard/leave",
  icon: Calendar,
  items: [
    { title: "Dashboard", href: "/dashboard/leave" },
    { title: "Requests", href: "/dashboard/leave/requests" },
    { title: "Balances", href: "/dashboard/leave/balances" },
    { title: "Calendar", href: "/dashboard/leave/calendar" },
    { title: "Reports", href: "/dashboard/leave/reports" },
    { title: "Settings", href: "/dashboard/leave/settings" }
  ]
}
\`\`\`

### 2. Database Setup
Run the leave types seeder:
\`\`\`bash
npm run seed:leave-types
\`\`\`

### 3. Environment Variables
Ensure these environment variables are set:
- MONGODB_URI (should already be configured)
- Any leave-specific configuration

### 4. Install Dependencies
Check if any new dependencies are needed:
\`\`\`bash
npm install date-fns
\`\`\`

## Testing Tasks

### 1. API Testing
Test all leave management endpoints:
\`\`\`bash
# Test leave requests API
curl http://localhost:3000/api/leave/requests

# Test leave types API
curl http://localhost:3000/api/leave/types
\`\`\`

### 2. Frontend Testing
- Navigate to /dashboard/leave
- Test leave request creation
- Test approval workflows
- Verify calendar functionality

### 3. Integration Testing
- Test employee data integration
- Verify payroll integration
- Check dashboard statistics

## Manual Review Required

### 1. DashboardShell Wrapping
Review and manually wrap page components in DashboardShell:
- app/(dashboard)/dashboard/leave/page.tsx
- All sub-pages in leave directory

### 2. Styling Adjustments
- Verify Tailwind classes compatibility
- Check responsive design
- Update any custom CSS

### 3. Authentication Integration
- Verify user permissions
- Test role-based access
- Check API authentication

### 4. Payroll Integration
- Update PayrollService for leave deductions
- Integrate leave data in salary calculations
- Test payroll processing with leave data

## Optional Enhancements

### 1. Email Notifications
Set up email notifications for leave requests

### 2. Calendar Integration
Integrate with external calendar systems

### 3. Mobile Optimization
Test and optimize mobile experience

### 4. Performance Optimization
- Implement caching
- Optimize database queries
- Add pagination where needed

## Verification Checklist

- [ ] All API endpoints respond correctly
- [ ] Frontend components render without errors
- [ ] Navigation works properly
- [ ] Database models are accessible
- [ ] Leave request workflow functions
- [ ] Payroll integration works
- [ ] User permissions are enforced
- [ ] Responsive design works on mobile
- [ ] Error handling works correctly
- [ ] Performance is acceptable

## Rollback Plan

If issues occur, restore from backup:
\`\`\`bash
# Backup was created at: ${CONFIG.BACKUP_DIR}-[timestamp]
# Restore if needed
\`\`\`
`;

    if (!CONFIG.DRY_RUN) {
      fs.writeFileSync(tasksFile, tasks);
      log.substep('Created POST_MIGRATION_TASKS.md');
    } else {
      log.substep('[DRY RUN] Would create POST_MIGRATION_TASKS.md');
    }

    log.success('Post-migration tasks file created');
  }
}

// Main migration orchestrator
class LeaveMigrationOrchestrator {
  static async run() {
    console.log(`${colors.bright}${colors.cyan}
╔══════════════════════════════════════════════════════════════╗
║                Leave Management Migration Script             ║
║              From Kawandamahrsystem to TCM Suite             ║
╚══════════════════════════════════════════════════════════════╝
${colors.reset}`);

    log.info(`Source: ${CONFIG.SOURCE_PROJECT}`);
    log.info(`Target: ${CONFIG.TARGET_PROJECT}`);
    log.info(`Dry Run: ${CONFIG.DRY_RUN ? 'YES' : 'NO'}`);

    if (CONFIG.DRY_RUN) {
      log.warning('DRY RUN MODE - No files will be modified');
    }

    try {
      // Verify source and target projects exist
      if (!fs.existsSync(CONFIG.SOURCE_PROJECT)) {
        throw new Error(`Source project not found: ${CONFIG.SOURCE_PROJECT}`);
      }

      if (!fs.existsSync(CONFIG.TARGET_PROJECT)) {
        throw new Error(`Target project not found: ${CONFIG.TARGET_PROJECT}`);
      }

      // Create backup
      if (!CONFIG.DRY_RUN) {
        FileOperations.createBackup();
      }

      // Execute migration phases
      await MigrationTasks.migrateModels();
      await MigrationTasks.migrateServices();
      await MigrationTasks.migrateAPIRoutes();
      await MigrationTasks.migrateFrontendComponents();
      await MigrationTasks.migrateHooksAndTypes();
      await MigrationTasks.migrateDashboardPages();
      await MigrationTasks.migrateSupportingFiles();
      await MigrationTasks.updatePayrollIntegration();
      await MigrationTasks.updateNavigation();
      await MigrationTasks.createPostMigrationTasks();

      console.log(`${colors.bright}${colors.green}
╔══════════════════════════════════════════════════════════════╗
║                    Migration Completed!                     ║
╚══════════════════════════════════════════════════════════════╝
${colors.reset}`);

      log.success('Leave Management system successfully migrated');
      log.info('Please review POST_MIGRATION_TASKS.md for next steps');

      if (CONFIG.DRY_RUN) {
        log.info('To execute the migration, set DRY_RUN to false and run again');
      }

    } catch (error) {
      log.error(`Migration failed: ${error.message}`);
      process.exit(1);
    }
  }
}

// Execute migration if run directly
if (require.main === module) {
  LeaveMigrationOrchestrator.run();
}

module.exports = { LeaveMigrationOrchestrator, CONFIG };
