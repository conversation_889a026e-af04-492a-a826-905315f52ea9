#!/usr/bin/env node

/**
 * Script to seed default leave types
 */

const path = require('path');

// Set up the environment
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

async function runSeeder() {
  try {
    console.log('🌱 Starting leave types seeding...');
    
    // Import the seeder function
    const { runLeaveTypesSeeder } = require('../lib/seeders/leave-types-seeder.ts');
    
    // Run the seeder
    await runLeaveTypesSeeder();
    
    console.log('✅ Leave types seeding completed successfully!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Leave types seeding failed:', error.message);
    console.error(error);
    process.exit(1);
  }
}

// Run the seeder
runSeeder();
