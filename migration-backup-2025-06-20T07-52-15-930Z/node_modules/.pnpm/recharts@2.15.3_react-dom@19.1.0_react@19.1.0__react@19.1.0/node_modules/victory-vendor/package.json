{"name": "victory-vendor", "version": "36.9.2", "description": "Vendored dependencies for Victory", "keywords": ["data visualization", "React", "d3", "charting"], "repository": {"type": "git", "url": "https://github.com/FormidableLabs/victory"}, "homepage": "https://commerce.nearform.com/open-source/victory", "author": "Formidable", "license": "MIT AND ISC", "exports": {"./package.json": "./package.json", "./d3-*": {"types": "./d3-*.d.ts", "import": "./es/d3-*.js", "default": "./lib/d3-*.js"}}, "dependencies": {"@types/d3-array": "^3.0.3", "@types/d3-ease": "^3.0.0", "@types/d3-interpolate": "^3.0.1", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-time": "^3.0.0", "@types/d3-timer": "^3.0.0", "d3-array": "^3.1.6", "d3-ease": "^3.0.1", "d3-interpolate": "^3.0.1", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-time": "^3.0.0", "d3-timer": "^3.0.1"}, "devDependencies": {"d3-color": "^3.1.0", "d3-format": "^3.1.0", "d3-path": "^3.0.1", "d3-time-format": "^4.1.0", "d3-voronoi": "^1.1.4", "internmap": "^2.0.3", "execa": "^6.1.0", "rimraf": "^3.0.2"}, "publishConfig": {"provenance": true}, "wireit": {"build": {"command": "node ./scripts/build.js", "files": [".babelrc.js", "scripts/build.js", "node_modules/**"], "output": ["es/**", "lib/**", "lib-vendor/**", "d3-*"], "packageLocks": ["pnpm-lock.yaml"]}, "build:lib:esm": {"dependencies": ["build"]}, "build:lib:cjs": {"dependencies": ["build"]}, "build:dist": {"dependencies": ["build"]}, "build:dist:dev": {"dependencies": ["build"]}, "build:dist:min": {"dependencies": ["build"]}, "check": {"dependencies": ["types:check", "jest", "format", "lint"]}, "types:check": {"command": "echo \"No types to check here\"", "files": [], "output": []}, "types:create": {"dependencies": ["build"]}, "format": {"command": "nps format:pkg", "files": ["scripts/**", "*.json", "../../.prettierignore", "../../.prettierrc.json"], "output": [], "packageLocks": ["pnpm-lock.yaml"]}, "format:fix": {"command": "pnpm run format || nps format:pkg:fix", "files": ["scripts/**", "*.json", "../../.prettierignore", "../../.prettierrc.json"], "output": [], "packageLocks": ["pnpm-lock.yaml"]}, "lint": {"command": "nps \"lint:base scripts\"", "files": ["scripts/**", "../../.eslintrc.js"], "output": [], "packageLocks": ["pnpm-lock.yaml"]}, "lint:fix": {"command": "pnpm run lint || nps \"lint:base --fix scripts\"", "files": ["scripts/**", "../../.eslintrc.js"], "output": [], "packageLocks": ["pnpm-lock.yaml"]}, "jest": {"command": "echo victory-vendor has no tests", "files": [], "output": []}}, "scripts": {"build": "wireit", "build:lib": "wireit", "build:lib:esm": "wireit", "build:lib:cjs": "wireit", "build:dist": "wireit", "build:dist:dev": "wireit", "build:dist:min": "wireit", "check": "wireit", "types:check": "wireit", "types:create": "wireit", "format": "wireit", "format:fix": "wireit", "lint": "wireit", "lint:fix": "wireit", "jest": "wireit"}}