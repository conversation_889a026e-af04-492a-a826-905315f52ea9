{"version": 3, "names": ["_path", "data", "require", "_utils", "_configError", "PACKAGE_FILENAME", "readConfigPackage", "makeStaticFileCache", "filepath", "content", "options", "JSON", "parse", "err", "ConfigError", "message", "Error", "Array", "isArray", "dirname", "path", "findPackageData", "pkg", "directories", "isPackage", "basename", "push", "join", "nextLoc"], "sources": ["../../../src/config/files/package.ts"], "sourcesContent": ["import path from \"node:path\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport { makeStaticFileCache } from \"./utils.ts\";\n\nimport type { ConfigFile, FilePackageData } from \"./types.ts\";\n\nimport ConfigError from \"../../errors/config-error.ts\";\n\nconst PACKAGE_FILENAME = \"package.json\";\n\nconst readConfigPackage = makeStaticFileCache(\n  (filepath, content): ConfigFile => {\n    let options;\n    try {\n      options = JSON.parse(content) as unknown;\n    } catch (err) {\n      throw new ConfigError(\n        `Error while parsing JSON - ${err.message}`,\n        filepath,\n      );\n    }\n\n    if (!options) throw new Error(`${filepath}: No config detected`);\n\n    if (typeof options !== \"object\") {\n      throw new ConfigError(\n        `Config returned typeof ${typeof options}`,\n        filepath,\n      );\n    }\n    if (Array.isArray(options)) {\n      throw new ConfigError(`Expected config object but found array`, filepath);\n    }\n\n    return {\n      filepath,\n      dirname: path.dirname(filepath),\n      options,\n    };\n  },\n);\n\n/**\n * Find metadata about the package that this file is inside of. Resolution\n * of Babel's config requires general package information to decide when to\n * search for .babelrc files\n */\nexport function* findPackageData(filepath: string): Handler<FilePackageData> {\n  let pkg = null;\n  const directories = [];\n  let isPackage = true;\n\n  let dirname = path.dirname(filepath);\n  while (!pkg && path.basename(dirname) !== \"node_modules\") {\n    directories.push(dirname);\n\n    pkg = yield* readConfigPackage(path.join(dirname, PACKAGE_FILENAME));\n\n    const nextLoc = path.dirname(dirname);\n    if (dirname === nextLoc) {\n      isPackage = false;\n      break;\n    }\n    dirname = nextLoc;\n  }\n\n  return { filepath, directories, pkg, isPackage };\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,MAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAE,MAAA,GAAAD,OAAA;AAIA,IAAAE,YAAA,GAAAF,OAAA;AAEA,MAAMG,gBAAgB,GAAG,cAAc;AAEvC,MAAMC,iBAAiB,GAAG,IAAAC,0BAAmB,EAC3C,CAACC,QAAQ,EAAEC,OAAO,KAAiB;EACjC,IAAIC,OAAO;EACX,IAAI;IACFA,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAY;EAC1C,CAAC,CAAC,OAAOI,GAAG,EAAE;IACZ,MAAM,IAAIC,oBAAW,CACnB,8BAA8BD,GAAG,CAACE,OAAO,EAAE,EAC3CP,QACF,CAAC;EACH;EAEA,IAAI,CAACE,OAAO,EAAE,MAAM,IAAIM,KAAK,CAAC,GAAGR,QAAQ,sBAAsB,CAAC;EAEhE,IAAI,OAAOE,OAAO,KAAK,QAAQ,EAAE;IAC/B,MAAM,IAAII,oBAAW,CACnB,0BAA0B,OAAOJ,OAAO,EAAE,EAC1CF,QACF,CAAC;EACH;EACA,IAAIS,KAAK,CAACC,OAAO,CAACR,OAAO,CAAC,EAAE;IAC1B,MAAM,IAAII,oBAAW,CAAC,wCAAwC,EAAEN,QAAQ,CAAC;EAC3E;EAEA,OAAO;IACLA,QAAQ;IACRW,OAAO,EAAEC,MAAGA,CAAC,CAACD,OAAO,CAACX,QAAQ,CAAC;IAC/BE;EACF,CAAC;AACH,CACF,CAAC;AAOM,UAAUW,eAAeA,CAACb,QAAgB,EAA4B;EAC3E,IAAIc,GAAG,GAAG,IAAI;EACd,MAAMC,WAAW,GAAG,EAAE;EACtB,IAAIC,SAAS,GAAG,IAAI;EAEpB,IAAIL,OAAO,GAAGC,MAAGA,CAAC,CAACD,OAAO,CAACX,QAAQ,CAAC;EACpC,OAAO,CAACc,GAAG,IAAIF,MAAGA,CAAC,CAACK,QAAQ,CAACN,OAAO,CAAC,KAAK,cAAc,EAAE;IACxDI,WAAW,CAACG,IAAI,CAACP,OAAO,CAAC;IAEzBG,GAAG,GAAG,OAAOhB,iBAAiB,CAACc,MAAGA,CAAC,CAACO,IAAI,CAACR,OAAO,EAAEd,gBAAgB,CAAC,CAAC;IAEpE,MAAMuB,OAAO,GAAGR,MAAGA,CAAC,CAACD,OAAO,CAACA,OAAO,CAAC;IACrC,IAAIA,OAAO,KAAKS,OAAO,EAAE;MACvBJ,SAAS,GAAG,KAAK;MACjB;IACF;IACAL,OAAO,GAAGS,OAAO;EACnB;EAEA,OAAO;IAAEpB,QAAQ;IAAEe,WAAW;IAAED,GAAG;IAAEE;EAAU,CAAC;AAClD;AAAC", "ignoreList": []}