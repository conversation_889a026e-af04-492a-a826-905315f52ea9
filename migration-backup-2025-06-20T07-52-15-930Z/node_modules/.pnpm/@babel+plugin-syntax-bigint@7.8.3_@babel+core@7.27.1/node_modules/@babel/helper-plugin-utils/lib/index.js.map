{"version": 3, "names": ["apiPolyfills", "assertVersion", "api", "range", "throwVersionError", "version", "Object", "assign", "targets", "assumption", "undefined", "addExternalDependency", "declare", "builder", "options", "dirname", "clonedA<PERSON>", "name", "keys", "copyApiObject", "declarePreset", "exports", "proto", "test", "getPrototypeOf", "hasOwnProperty", "call", "Number", "isInteger", "Error", "limit", "stackTraceLimit", "err", "slice", "code"], "sources": ["../src/index.ts"], "sourcesContent": ["import type {\n  Plugin<PERSON>I,\n  PluginObject,\n  PluginPass,\n  PresetAPI,\n  PresetObject,\n} from \"@babel/core\";\n\ntype APIPolyfillFactory<T extends keyof PluginAPI> = (\n  api: PluginAPI,\n) => PluginAPI[T];\n\ntype APIPolyfills = {\n  assertVersion: APIPolyfillFactory<\"assertVersion\">;\n};\n\nconst apiPolyfills: APIPolyfills = {\n  // Not supported by Babel 7 and early versions of Babel 7 beta.\n  // It's important that this is polyfilled for older Babel versions\n  // since it's needed to report the version mismatch.\n  assertVersion: (api: PluginAPI) => (range: number | string) => {\n    throwVersionError(range, api.version);\n  },\n};\nif (!process.env.BABEL_8_BREAKING) {\n  Object.assign(apiPolyfills, {\n    // This is supported starting from Babel 7.13\n    targets: () => (): any => {\n      return {};\n    },\n    // This is supported starting from Babel 7.13\n    assumption: () => (): any => {\n      return undefined;\n    },\n    // This is supported starting from Babel 7.17\n    addExternalDependency: () => () => {},\n  });\n}\n\nexport function declare<State = object, Option = object>(\n  builder: (\n    api: PluginAPI,\n    options: Option,\n    dirname: string,\n  ) => PluginObject<State & PluginPass>,\n): (\n  api: PluginAPI,\n  options: Option,\n  dirname: string,\n) => PluginObject<State & PluginPass> {\n  return (api, options: Option, dirname: string) => {\n    let clonedApi: PluginAPI;\n\n    for (const name of Object.keys(\n      apiPolyfills,\n    ) as (keyof typeof apiPolyfills)[]) {\n      if (api[name]) continue;\n\n      clonedApi ??= copyApiObject(api);\n      clonedApi[name] = apiPolyfills[name](clonedApi);\n    }\n\n    // @ts-expect-error options || {} may not be assigned to Options\n    return builder(clonedApi ?? api, options || {}, dirname);\n  };\n}\n\nexport const declarePreset = declare as <Option = object>(\n  builder: (api: PresetAPI, options: Option, dirname: string) => PresetObject,\n) => (api: PresetAPI, options: Option, dirname: string) => PresetObject;\n\nfunction copyApiObject(api: PluginAPI): PluginAPI {\n  // Babel >= 7 <= beta.41 passed the API as a new object that had\n  // babel/core as the prototype. While slightly faster, it also\n  // means that the Object.assign copy below fails. Rather than\n  // keep complexity, the Babel 6 behavior has been reverted and this\n  // normalizes all that for Babel 7.\n  let proto = null;\n  if (typeof api.version === \"string\" && /^7\\./.test(api.version)) {\n    proto = Object.getPrototypeOf(api);\n    if (\n      proto &&\n      (!Object.hasOwn(proto, \"version\") ||\n        !Object.hasOwn(proto, \"transform\") ||\n        !Object.hasOwn(proto, \"template\") ||\n        !Object.hasOwn(proto, \"types\"))\n    ) {\n      proto = null;\n    }\n  }\n\n  return {\n    ...proto,\n    ...api,\n  };\n}\n\nfunction throwVersionError(range: string | number, version: string) {\n  if (typeof range === \"number\") {\n    if (!Number.isInteger(range)) {\n      throw new Error(\"Expected string or integer value.\");\n    }\n    range = `^${range}.0.0-0`;\n  }\n  if (typeof range !== \"string\") {\n    throw new Error(\"Expected string or integer value.\");\n  }\n\n  const limit = Error.stackTraceLimit;\n\n  if (typeof limit === \"number\" && limit < 25) {\n    // Bump up the limit if needed so that users are more likely\n    // to be able to see what is calling Babel.\n    Error.stackTraceLimit = 25;\n  }\n\n  let err;\n  if (version.slice(0, 2) === \"7.\") {\n    err = new Error(\n      `Requires Babel \"^7.0.0-beta.41\", but was loaded with \"${version}\". ` +\n        `You'll need to update your @babel/core version.`,\n    );\n  } else {\n    err = new Error(\n      `Requires Babel \"${range}\", but was loaded with \"${version}\". ` +\n        `If you are sure you have a compatible version of @babel/core, ` +\n        `it is likely that something in your build process is loading the ` +\n        `wrong version. Inspect the stack trace of this error to look for ` +\n        `the first entry that doesn't mention \"@babel/core\" or \"babel-core\" ` +\n        `to see what is calling Babel.`,\n    );\n  }\n\n  if (typeof limit === \"number\") {\n    Error.stackTraceLimit = limit;\n  }\n\n  throw Object.assign(err, {\n    code: \"BABEL_VERSION_UNSUPPORTED\",\n    version,\n    range,\n  } as any);\n}\n"], "mappings": ";;;;;;;AAgBA,MAAMA,YAA0B,GAAG;EAIjCC,aAAa,EAAGC,GAAc,IAAMC,KAAsB,IAAK;IAC7DC,iBAAiB,CAACD,KAAK,EAAED,GAAG,CAACG,OAAO,CAAC;EACvC;AACF,CAAC;AACkC;EACjCC,MAAM,CAACC,MAAM,CAACP,YAAY,EAAE;IAE1BQ,OAAO,EAAEA,CAAA,KAAM,MAAW;MACxB,OAAO,CAAC,CAAC;IACX,CAAC;IAEDC,UAAU,EAAEA,CAAA,KAAM,MAAW;MAC3B,OAAOC,SAAS;IAClB,CAAC;IAEDC,qBAAqB,EAAEA,CAAA,KAAM,MAAM,CAAC;EACtC,CAAC,CAAC;AACJ;AAEO,SAASC,OAAOA,CACrBC,OAIqC,EAKD;EACpC,OAAO,CAACX,GAAG,EAAEY,OAAe,EAAEC,OAAe,KAAK;IAChD,IAAIC,SAAoB;IAExB,KAAK,MAAMC,IAAI,IAAIX,MAAM,CAACY,IAAI,CAC5BlB,YACF,CAAC,EAAmC;MAClC,IAAIE,GAAG,CAACe,IAAI,CAAC,EAAE;MAEfD,SAAS,WAATA,SAAS,GAATA,SAAS,GAAKG,aAAa,CAACjB,GAAG,CAAC;MAChCc,SAAS,CAACC,IAAI,CAAC,GAAGjB,YAAY,CAACiB,IAAI,CAAC,CAACD,SAAS,CAAC;IACjD;IAGA,OAAOH,OAAO,CAACG,SAAS,WAATA,SAAS,GAAId,GAAG,EAAEY,OAAO,IAAI,CAAC,CAAC,EAAEC,OAAO,CAAC;EAC1D,CAAC;AACH;AAEO,MAAMK,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAGR,OAE0C;AAEvE,SAASO,aAAaA,CAACjB,GAAc,EAAa;EAMhD,IAAIoB,KAAK,GAAG,IAAI;EAChB,IAAI,OAAOpB,GAAG,CAACG,OAAO,KAAK,QAAQ,IAAI,MAAM,CAACkB,IAAI,CAACrB,GAAG,CAACG,OAAO,CAAC,EAAE;IAC/DiB,KAAK,GAAGhB,MAAM,CAACkB,cAAc,CAACtB,GAAG,CAAC;IAClC,IACEoB,KAAK,KACJ,CAACG,cAAA,CAAAC,IAAA,CAAcJ,KAAK,EAAE,SAAS,CAAC,IAC/B,CAACG,cAAA,CAAAC,IAAA,CAAcJ,KAAK,EAAE,WAAW,CAAC,IAClC,CAACG,cAAA,CAAAC,IAAA,CAAcJ,KAAK,EAAE,UAAU,CAAC,IACjC,CAACG,cAAA,CAAAC,IAAA,CAAcJ,KAAK,EAAE,OAAO,CAAC,CAAC,EACjC;MACAA,KAAK,GAAG,IAAI;IACd;EACF;EAEA,OAAAhB,MAAA,CAAAC,MAAA,KACKe,KAAK,EACLpB,GAAG;AAEV;AAEA,SAASE,iBAAiBA,CAACD,KAAsB,EAAEE,OAAe,EAAE;EAClE,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAI,CAACwB,MAAM,CAACC,SAAS,CAACzB,KAAK,CAAC,EAAE;MAC5B,MAAM,IAAI0B,KAAK,CAAC,mCAAmC,CAAC;IACtD;IACA1B,KAAK,GAAG,IAAIA,KAAK,QAAQ;EAC3B;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAM,IAAI0B,KAAK,CAAC,mCAAmC,CAAC;EACtD;EAEA,MAAMC,KAAK,GAAGD,KAAK,CAACE,eAAe;EAEnC,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,GAAG,EAAE,EAAE;IAG3CD,KAAK,CAACE,eAAe,GAAG,EAAE;EAC5B;EAEA,IAAIC,GAAG;EACP,IAAI3B,OAAO,CAAC4B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;IAChCD,GAAG,GAAG,IAAIH,KAAK,CACb,yDAAyDxB,OAAO,KAAK,GACnE,iDACJ,CAAC;EACH,CAAC,MAAM;IACL2B,GAAG,GAAG,IAAIH,KAAK,CACb,mBAAmB1B,KAAK,2BAA2BE,OAAO,KAAK,GAC7D,gEAAgE,GAChE,mEAAmE,GACnE,mEAAmE,GACnE,qEAAqE,GACrE,+BACJ,CAAC;EACH;EAEA,IAAI,OAAOyB,KAAK,KAAK,QAAQ,EAAE;IAC7BD,KAAK,CAACE,eAAe,GAAGD,KAAK;EAC/B;EAEA,MAAMxB,MAAM,CAACC,MAAM,CAACyB,GAAG,EAAE;IACvBE,IAAI,EAAE,2BAA2B;IACjC7B,OAAO;IACPF;EACF,CAAQ,CAAC;AACX", "ignoreList": []}