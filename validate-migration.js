#!/usr/bin/env node

/**
 * Migration Validation Script
 * Validates that the leave management migration was successful
 */

const fs = require('fs');
const path = require('path');

const TARGET_PROJECT = '/Users/<USER>/Documents/REACT BLOG APP/lasttcmsuit';

// Color codes
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✓ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}✗ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠ ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ ${msg}${colors.reset}`)
};

// Validation checks
const validationChecks = {
  models: [
    'models/leave/Leave.ts',
    'models/leave/LeaveType.ts',
    'models/leave/LeaveBalance.ts',
    'models/leave/LeaveAccrual.ts',
    'models/leave/LeaveAccrualRule.ts',
    'models/leave/LeaveApprovalWorkflow.ts',
    'models/leave/LeaveApprovalTemplate.ts',
    'models/leave/LeaveEncashment.ts',
    'models/leave/LeaveEncashmentRule.ts',
    'models/leave/LeaveSettings.ts'
  ],
  
  services: [
    'services/leave/LeaveService.ts',
    'services/leave/LeaveAccrualService.ts',
    'services/leave/LeaveApprovalWorkflowService.ts',
    'services/leave/LeaveEncashmentService.ts',
    'services/leave/LeaveReportingService.ts'
  ],
  
  apiRoutes: [
    'app/api/leave/requests/route.ts',
    'app/api/leave/types/route.ts',
    'app/api/leave/balances/route.ts',
    'app/api/leave/calendar/route.ts',
    'app/api/leave/reports/overview/route.ts',
    'app/api/dashboard/leave/route.ts',
    'app/api/payroll/leave-data/route.ts'
  ],
  
  components: [
    'components/leave-management/leave-management-page.tsx',
    'components/leave-management/leave-request-form.tsx',
    'components/leave-management/leave-requests-list.tsx',
    'components/leave-management/leave-calendar.tsx',
    'components/leave-management/leave-balances.tsx'
  ],
  
  pages: [
    'app/(dashboard)/dashboard/leave/page.tsx',
    'app/(dashboard)/dashboard/leave/requests/page.tsx',
    'app/(dashboard)/dashboard/leave/balances/page.tsx',
    'app/(dashboard)/dashboard/leave/calendar/page.tsx',
    'app/(dashboard)/dashboard/leave/reports/page.tsx'
  ],
  
  hooks: [
    'hooks/use-leave-requests.ts',
    'hooks/use-leave-types.ts',
    'hooks/use-leave-balances.ts'
  ],
  
  types: [
    'types/leave-request.ts'
  ]
};

function validateFiles(category, files) {
  console.log(`\n${colors.cyan}Validating ${category}:${colors.reset}`);
  
  let passed = 0;
  let failed = 0;
  
  files.forEach(file => {
    const filePath = path.join(TARGET_PROJECT, file);
    if (fs.existsSync(filePath)) {
      log.success(file);
      passed++;
    } else {
      log.error(`Missing: ${file}`);
      failed++;
    }
  });
  
  return { passed, failed };
}

function validateDirectoryStructure() {
  console.log(`\n${colors.cyan}Validating Directory Structure:${colors.reset}`);
  
  const requiredDirs = [
    'models/leave',
    'services/leave',
    'app/api/leave',
    'components/leave-management',
    'app/(dashboard)/dashboard/leave'
  ];
  
  let passed = 0;
  let failed = 0;
  
  requiredDirs.forEach(dir => {
    const dirPath = path.join(TARGET_PROJECT, dir);
    if (fs.existsSync(dirPath)) {
      log.success(dir);
      passed++;
    } else {
      log.error(`Missing directory: ${dir}`);
      failed++;
    }
  });
  
  return { passed, failed };
}

function checkFileContent() {
  console.log(`\n${colors.cyan}Checking File Content:${colors.reset}`);
  
  const checks = [
    {
      file: 'models/leave/Leave.ts',
      contains: 'mongoose',
      description: 'Leave model has mongoose import'
    },
    {
      file: 'services/leave/LeaveService.ts',
      contains: 'class LeaveService',
      description: 'LeaveService class exists'
    },
    {
      file: 'app/api/leave/requests/route.ts',
      contains: 'export async function GET',
      description: 'Leave requests API has GET handler'
    }
  ];
  
  let passed = 0;
  let failed = 0;
  
  checks.forEach(check => {
    const filePath = path.join(TARGET_PROJECT, check.file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes(check.contains)) {
        log.success(check.description);
        passed++;
      } else {
        log.error(`${check.description} - content check failed`);
        failed++;
      }
    } else {
      log.error(`${check.description} - file not found`);
      failed++;
    }
  });
  
  return { passed, failed };
}

function generateReport(results) {
  console.log(`\n${colors.cyan}Migration Validation Report:${colors.reset}`);
  console.log('═'.repeat(50));
  
  let totalPassed = 0;
  let totalFailed = 0;
  
  Object.entries(results).forEach(([category, result]) => {
    console.log(`${category}: ${result.passed} passed, ${result.failed} failed`);
    totalPassed += result.passed;
    totalFailed += result.failed;
  });
  
  console.log('═'.repeat(50));
  console.log(`Total: ${totalPassed} passed, ${totalFailed} failed`);
  
  if (totalFailed === 0) {
    log.success('Migration validation PASSED! 🎉');
    console.log('\nNext steps:');
    console.log('1. Review POST_MIGRATION_TASKS.md');
    console.log('2. Update navigation configuration');
    console.log('3. Run database seeders');
    console.log('4. Test the application');
  } else {
    log.error('Migration validation FAILED!');
    console.log('\nPlease check the missing files and re-run the migration.');
  }
  
  return totalFailed === 0;
}

function main() {
  console.log(`${colors.cyan}
╔══════════════════════════════════════════════════════════════╗
║                Migration Validation Script                  ║
╚══════════════════════════════════════════════════════════════╝
${colors.reset}`);
  
  log.info(`Validating migration in: ${TARGET_PROJECT}`);
  
  if (!fs.existsSync(TARGET_PROJECT)) {
    log.error(`Target project not found: ${TARGET_PROJECT}`);
    process.exit(1);
  }
  
  const results = {};
  
  // Validate directory structure
  results.directories = validateDirectoryStructure();
  
  // Validate each category of files
  Object.entries(validationChecks).forEach(([category, files]) => {
    results[category] = validateFiles(category, files);
  });
  
  // Check file content
  results.content = checkFileContent();
  
  // Generate final report
  const success = generateReport(results);
  
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main();
}
