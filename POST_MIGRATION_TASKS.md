# Post-Migration Tasks for Leave Management System

## Immediate Tasks (Required)

### 1. Update Navigation
Add leave management menu items to your navigation configuration:
```typescript
{
  title: "Leave Management",
  href: "/dashboard/leave",
  icon: Calendar,
  items: [
    { title: "Dashboard", href: "/dashboard/leave" },
    { title: "Requests", href: "/dashboard/leave/requests" },
    { title: "Balances", href: "/dashboard/leave/balances" },
    { title: "Calendar", href: "/dashboard/leave/calendar" },
    { title: "Reports", href: "/dashboard/leave/reports" },
    { title: "Settings", href: "/dashboard/leave/settings" }
  ]
}
```

### 2. Database Setup
Run the leave types seeder:
```bash
npm run seed:leave-types
```

### 3. Environment Variables
Ensure these environment variables are set:
- MONGODB_URI (should already be configured)
- Any leave-specific configuration

### 4. Install Dependencies
Check if any new dependencies are needed:
```bash
npm install date-fns
```

## Testing Tasks

### 1. API Testing
Test all leave management endpoints:
```bash
# Test leave requests API
curl http://localhost:3000/api/leave/requests

# Test leave types API
curl http://localhost:3000/api/leave/types
```

### 2. Frontend Testing
- Navigate to /dashboard/leave
- Test leave request creation
- Test approval workflows
- Verify calendar functionality

### 3. Integration Testing
- Test employee data integration
- Verify payroll integration
- Check dashboard statistics

## Manual Review Required

### 1. DashboardShell Wrapping
Review and manually wrap page components in DashboardShell:
- app/(dashboard)/dashboard/leave/page.tsx
- All sub-pages in leave directory

### 2. Styling Adjustments
- Verify Tailwind classes compatibility
- Check responsive design
- Update any custom CSS

### 3. Authentication Integration
- Verify user permissions
- Test role-based access
- Check API authentication

### 4. Payroll Integration
- Update PayrollService for leave deductions
- Integrate leave data in salary calculations
- Test payroll processing with leave data

## Optional Enhancements

### 1. Email Notifications
Set up email notifications for leave requests

### 2. Calendar Integration
Integrate with external calendar systems

### 3. Mobile Optimization
Test and optimize mobile experience

### 4. Performance Optimization
- Implement caching
- Optimize database queries
- Add pagination where needed

## Verification Checklist

- [ ] All API endpoints respond correctly
- [ ] Frontend components render without errors
- [ ] Navigation works properly
- [ ] Database models are accessible
- [ ] Leave request workflow functions
- [ ] Payroll integration works
- [ ] User permissions are enforced
- [ ] Responsive design works on mobile
- [ ] Error handling works correctly
- [ ] Performance is acceptable

## Rollback Plan

If issues occur, restore from backup:
```bash
# Backup was created at: ./migration-backup-[timestamp]
# Restore if needed
```
