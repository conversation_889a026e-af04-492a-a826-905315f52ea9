#!/bin/bash

# Leave Management Migration Runner
# This script provides a safe way to run the migration with options

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Print colored output
print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_header() {
    echo -e "${CYAN}
╔══════════════════════════════════════════════════════════════╗
║                Leave Management Migration                    ║
║              Kawandamahrsystem → TCM Enterprise              ║
╚══════════════════════════════════════════════════════════════╝
${NC}"
}

# Check if Node.js is available
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed or not in PATH"
        exit 1
    fi
    print_success "Node.js found: $(node --version)"
}

# Check if source and target projects exist
check_projects() {
    SOURCE_PROJECT="/Users/<USER>/Documents/REACT BLOG APP/KAWANDAMA PROJECTS/kawandamahrsystem"
    TARGET_PROJECT="/Users/<USER>/Documents/REACT BLOG APP/lasttcmsuit"
    
    if [ ! -d "$SOURCE_PROJECT" ]; then
        print_error "Source project not found: $SOURCE_PROJECT"
        exit 1
    fi
    print_success "Source project found"
    
    if [ ! -d "$TARGET_PROJECT" ]; then
        print_error "Target project not found: $TARGET_PROJECT"
        exit 1
    fi
    print_success "Target project found"
}

# Show migration options
show_options() {
    echo ""
    print_info "Migration Options:"
    echo "  1. Dry Run (Preview changes without executing)"
    echo "  2. Full Migration (Execute all changes)"
    echo "  3. Exit"
    echo ""
}

# Run dry run
run_dry_run() {
    print_info "Running migration in DRY RUN mode..."
    echo ""
    
    # Modify the script to set DRY_RUN to true
    sed -i.bak 's/DRY_RUN: false/DRY_RUN: true/' migrate-leave-management.js
    
    # Run the migration
    node migrate-leave-management.js
    
    # Restore original script
    mv migrate-leave-management.js.bak migrate-leave-management.js
    
    echo ""
    print_success "Dry run completed. Review the output above."
    print_info "No files were modified during dry run."
}

# Run full migration
run_full_migration() {
    print_warning "This will modify your TCM project files!"
    print_info "A backup will be created automatically."
    echo ""
    read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Running full migration..."
        echo ""
        
        # Ensure DRY_RUN is false
        sed -i.bak 's/DRY_RUN: true/DRY_RUN: false/' migrate-leave-management.js
        
        # Run the migration
        node migrate-leave-management.js
        
        # Restore original script
        mv migrate-leave-management.js.bak migrate-leave-management.js
        
        echo ""
        print_success "Migration completed!"
        print_info "Please check POST_MIGRATION_TASKS.md for next steps."
    else
        print_info "Migration cancelled."
    fi
}

# Main execution
main() {
    print_header
    
    print_info "Checking prerequisites..."
    check_node
    check_projects
    
    while true; do
        show_options
        read -p "Select an option (1-3): " choice
        
        case $choice in
            1)
                run_dry_run
                ;;
            2)
                run_full_migration
                break
                ;;
            3)
                print_info "Exiting..."
                exit 0
                ;;
            *)
                print_error "Invalid option. Please select 1, 2, or 3."
                ;;
        esac
        
        echo ""
        read -p "Press Enter to continue..."
        clear
        print_header
    done
}

# Check if migration script exists
if [ ! -f "migrate-leave-management.js" ]; then
    print_error "Migration script not found: migrate-leave-management.js"
    print_info "Please ensure you're in the correct directory."
    exit 1
fi

# Run main function
main
