#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/REACT BLOG APP/lasttcmsuit/node_modules/.pnpm/btoa@1.2.1/node_modules/btoa/bin/node_modules:/Users/<USER>/Documents/REACT BLOG APP/lasttcmsuit/node_modules/.pnpm/btoa@1.2.1/node_modules/btoa/node_modules:/Users/<USER>/Documents/REACT BLOG APP/lasttcmsuit/node_modules/.pnpm/btoa@1.2.1/node_modules:/Users/<USER>/Documents/REACT BLOG APP/lasttcmsuit/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/REACT BLOG APP/lasttcmsuit/node_modules/.pnpm/btoa@1.2.1/node_modules/btoa/bin/node_modules:/Users/<USER>/Documents/REACT BLOG APP/lasttcmsuit/node_modules/.pnpm/btoa@1.2.1/node_modules/btoa/node_modules:/Users/<USER>/Documents/REACT BLOG APP/lasttcmsuit/node_modules/.pnpm/btoa@1.2.1/node_modules:/Users/<USER>/Documents/REACT BLOG APP/lasttcmsuit/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/btoa.js" "$@"
else
  exec node  "$basedir/../../bin/btoa.js" "$@"
fi
