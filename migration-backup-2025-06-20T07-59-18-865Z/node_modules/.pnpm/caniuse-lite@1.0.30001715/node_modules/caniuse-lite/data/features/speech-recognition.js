module.exports={A:{A:{"2":"K D E F A B nC"},B:{"2":"C L M G N O P","514":"0 9 Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB I"},C:{"2":"1 2 oC MC J QB K D E F A B C L M G N O P RB rC sC","322":"0 3 4 5 6 7 8 9 SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB NC xB OC yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC Q H R PC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB I QC FC RC pC qC"},D:{"2":"1 2 3 4 5 J QB K D E F A B C L M G N O P RB","164":"0 6 7 8 9 SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB NC xB OC yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC Q H R S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z AB BB CB DB EB FB GB HB IB JB KB LB MB NB OB PB I QC FC RC"},E:{"2":"J QB K D E F A B C L M tC SC uC vC wC xC TC GC HC yC","1060":"G zC 0C UC VC IC 1C JC WC XC YC ZC aC 2C KC bC cC dC eC fC 3C LC gC hC iC jC kC 4C"},F:{"2":"1 2 3 4 5 6 7 F B C G N O P RB 5C 6C 7C 8C GC lC 9C HC","514":"0 8 SB TB UB VB WB XB YB ZB aB bB cB dB eB fB gB hB iB jB kB lB mB nB oB pB qB rB sB tB uB vB wB xB yB zB 0B 1B 2B 3B 4B 5B 6B 7B 8B 9B AC BC CC DC EC Q H R PC S T U V W X Y Z a b c d e f g h i j k l m n o p q r s t u v w x y z"},G:{"2":"E SC AD mC BD CD DD ED FD GD HD ID JD KD LD MD ND OD PD QD RD","1060":"SD TD UC VC IC UD JC WC XC YC ZC aC VD KC bC cC dC eC fC WD LC gC hC iC jC kC"},H:{"2":"XD"},I:{"2":"MC J I YD ZD aD bD mC cD dD"},J:{"2":"D A"},K:{"2":"A B C GC lC HC","164":"H"},L:{"164":"I"},M:{"2":"FC"},N:{"2":"A B"},O:{"164":"IC"},P:{"164":"1 2 3 4 5 6 7 8 J eD fD gD hD iD TC jD kD lD mD nD JC KC LC oD"},Q:{"164":"pD"},R:{"164":"qD"},S:{"322":"rD sD"}},B:7,C:"Speech Recognition API",D:true};
