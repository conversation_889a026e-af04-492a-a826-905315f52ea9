// lib/services/dashboard/leave-dashboard-service.ts
import { connectToDatabase } from '@/lib/database';
import logger, { LogCategory } from '@/lib/logger';
import mongoose from 'mongoose';

// Import existing models
import Leave from '@/models/leave/Leave';
import LeaveType from '@/models/leave/LeaveType';
import LeaveBalance from '@/models/leave/LeaveBalance';
import Employee from '@/models/Employee';
import User from '@/models/User';
import Department from '@/models/Department';

/**
 * Interface for dashboard leave request
 */
export interface DashboardLeaveRequest {
  id: string;
  leaveId: string;
  employee: {
    id: string;
    name: string;
    avatar?: string;
    initials: string;
    department?: string;
    position?: string;
  };
  submittedBy: {
    id: string;
    name: string;
    avatar?: string;
  };
  leaveType: {
    id: string;
    name: string;
    code: string;
    color: string;
    isPaid: boolean;
  };
  startDate: Date;
  endDate: Date;
  duration: number;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  submittedAt: Date;
  approvedBy?: {
    id: string;
    name: string;
  };
  approvalDate?: Date;
  rejectionReason?: string;
  attachments?: string[];
  notes?: string;
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  daysUntilStart: number;
  isOverdue: boolean;
}

/**
 * Interface for leave statistics
 */
export interface LeaveStats {
  totalRequests: number;
  pendingRequests: number;
  approvedRequests: number;
  rejectedRequests: number;
  totalDaysRequested: number;
  totalDaysApproved: number;
  
  // Current period stats
  thisMonthRequests: number;
  thisWeekRequests: number;
  todayRequests: number;
  
  // By leave type
  byLeaveType: Array<{
    leaveType: string;
    leaveTypeId: string;
    color: string;
    count: number;
    totalDays: number;
    percentage: number;
  }>;
  
  // By status
  byStatus: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  
  // By department
  byDepartment: Array<{
    department: string;
    departmentId: string;
    count: number;
    totalDays: number;
  }>;
  
  // Trends
  trends: Array<{
    date: string;
    submitted: number;
    approved: number;
    rejected: number;
  }>;
  
  // Performance metrics
  averageApprovalTime: number; // in hours
  approvalRate: number; // percentage
  overdueRequests: number;
}

/**
 * Interface for leave balance summary
 */
export interface LeaveBalanceSummary {
  employeeId: string;
  employeeName: string;
  department?: string;
  balances: Array<{
    leaveType: {
      id: string;
      name: string;
      code: string;
      color: string;
    };
    totalDays: number;
    usedDays: number;
    pendingDays: number;
    remainingDays: number;
    utilizationPercentage: number;
  }>;
  totalRemainingDays: number;
  totalUsedDays: number;
  totalPendingDays: number;
}

/**
 * Interface for upcoming leaves
 */
export interface UpcomingLeave {
  id: string;
  employee: {
    id: string;
    name: string;
    avatar?: string;
    department?: string;
  };
  leaveType: {
    name: string;
    color: string;
  };
  startDate: Date;
  endDate: Date;
  duration: number;
  daysUntilStart: number;
}

/**
 * Service for managing dashboard leave data
 */
export class LeaveDashboardService {
  /**
   * Get leave requests for dashboard display
   */
  async getLeaveRequests(
    userId: string,
    userRole: string,
    limit: number = 10,
    filters?: {
      status?: string[];
      leaveTypes?: string[];
      departments?: string[];
      startDate?: Date;
      endDate?: Date;
      employeeId?: string;
    }
  ): Promise<DashboardLeaveRequest[]> {
    try {
      await connectToDatabase();
      
      logger.info('Fetching leave requests for dashboard', LogCategory.DASHBOARD, {
        userId,
        userRole,
        limit,
        filters
      });

      // Build query
      const query: any = {};
      
      // Apply filters
      if (filters) {
        if (filters.status && filters.status.length > 0) {
          query.status = { $in: filters.status };
        }
        
        if (filters.leaveTypes && filters.leaveTypes.length > 0) {
          query.leaveTypeId = { $in: filters.leaveTypes.map(id => new mongoose.Types.ObjectId(id)) };
        }
        
        if (filters.employeeId) {
          query.employeeId = new mongoose.Types.ObjectId(filters.employeeId);
        }
        
        if (filters.startDate || filters.endDate) {
          query.startDate = {};
          if (filters.startDate) {
            query.startDate.$gte = filters.startDate;
          }
          if (filters.endDate) {
            query.startDate.$lte = filters.endDate;
          }
        }
      }

      // Fetch leave requests with populated data
      const leaveRequests = await Leave.find(query)
        .populate('employeeId', 'firstName lastName avatar position departmentId')
        .populate('leaveTypeId', 'name code color isPaid')
        .populate('createdBy', 'firstName lastName avatar')
        .populate('approvedBy', 'firstName lastName')
        .populate({
          path: 'employeeId',
          populate: {
            path: 'departmentId',
            select: 'name'
          }
        })
        .sort({ createdAt: -1 })
        .limit(limit)
        .lean();

      // Transform to dashboard format
      const dashboardRequests = leaveRequests.map(request => 
        this.transformLeaveRequestToDashboard(request)
      );

      // Apply department filter if specified
      let filteredRequests = dashboardRequests;
      if (filters?.departments && filters.departments.length > 0) {
        filteredRequests = dashboardRequests.filter(request => 
          request.employee.department && filters.departments!.includes(request.employee.department)
        );
      }

      logger.info('Successfully fetched leave requests', LogCategory.DASHBOARD, {
        userId,
        count: filteredRequests.length,
        limit
      });

      return filteredRequests;
    } catch (error) {
      logger.error('Error fetching leave requests', LogCategory.DASHBOARD, error);
      throw error;
    }
  }

  /**
   * Get leave statistics
   */
  async getLeaveStats(
    userId: string,
    userRole: string,
    period: 'day' | 'week' | 'month' | 'year' = 'month'
  ): Promise<LeaveStats> {
    try {
      await connectToDatabase();
      
      logger.info('Generating leave statistics', LogCategory.DASHBOARD, {
        userId,
        userRole,
        period
      });

      const now = new Date();
      const startDate = this.getStartDateForPeriod(period, now);

      // Get all leave requests for the period
      const allRequests = await Leave.find({
        createdAt: { $gte: startDate }
      })
        .populate('leaveTypeId', 'name code color')
        .populate('employeeId', 'departmentId')
        .populate({
          path: 'employeeId',
          populate: {
            path: 'departmentId',
            select: 'name'
          }
        })
        .lean();

      // Calculate basic statistics
      const totalRequests = allRequests.length;
      const pendingRequests = allRequests.filter(r => r.status === 'pending').length;
      const approvedRequests = allRequests.filter(r => r.status === 'approved').length;
      const rejectedRequests = allRequests.filter(r => r.status === 'rejected').length;
      
      const totalDaysRequested = allRequests.reduce((sum, r) => sum + r.duration, 0);
      const totalDaysApproved = allRequests
        .filter(r => r.status === 'approved')
        .reduce((sum, r) => sum + r.duration, 0);

      // Calculate period-specific stats
      const thisMonthRequests = allRequests.filter(r => 
        this.isInCurrentMonth(r.createdAt)
      ).length;
      
      const thisWeekRequests = allRequests.filter(r => 
        this.isInCurrentWeek(r.createdAt)
      ).length;
      
      const todayRequests = allRequests.filter(r => 
        this.isToday(r.createdAt)
      ).length;

      // Calculate breakdowns
      const byLeaveType = await this.calculateLeaveTypeBreakdown(allRequests);
      const byStatus = this.calculateStatusBreakdown(allRequests);
      const byDepartment = this.calculateDepartmentBreakdown(allRequests);
      const trends = await this.calculateLeaveTrends(period, now);

      // Calculate performance metrics
      const averageApprovalTime = await this.calculateAverageApprovalTime(period);
      const approvalRate = totalRequests > 0 ? (approvedRequests / totalRequests) * 100 : 0;
      const overdueRequests = await this.getOverdueRequestsCount();

      const stats: LeaveStats = {
        totalRequests,
        pendingRequests,
        approvedRequests,
        rejectedRequests,
        totalDaysRequested,
        totalDaysApproved,
        thisMonthRequests,
        thisWeekRequests,
        todayRequests,
        byLeaveType,
        byStatus,
        byDepartment,
        trends,
        averageApprovalTime,
        approvalRate,
        overdueRequests
      };

      logger.info('Successfully generated leave statistics', LogCategory.DASHBOARD, {
        userId,
        totalRequests,
        pendingRequests,
        approvedRequests
      });

      return stats;
    } catch (error) {
      logger.error('Error generating leave statistics', LogCategory.DASHBOARD, error);
      throw error;
    }
  }

  /**
   * Get upcoming leaves
   */
  async getUpcomingLeaves(
    userId: string,
    userRole: string,
    limit: number = 10,
    daysAhead: number = 30
  ): Promise<UpcomingLeave[]> {
    try {
      await connectToDatabase();
      
      const now = new Date();
      const futureDate = new Date(now.getTime() + daysAhead * 24 * 60 * 60 * 1000);

      const upcomingLeaves = await Leave.find({
        status: 'approved',
        startDate: {
          $gte: now,
          $lte: futureDate
        }
      })
        .populate('employeeId', 'firstName lastName avatar departmentId')
        .populate('leaveTypeId', 'name color')
        .populate({
          path: 'employeeId',
          populate: {
            path: 'departmentId',
            select: 'name'
          }
        })
        .sort({ startDate: 1 })
        .limit(limit)
        .lean();

      return upcomingLeaves.map(leave => ({
        id: leave._id.toString(),
        employee: {
          id: leave.employeeId._id.toString(),
          name: `${leave.employeeId.firstName} ${leave.employeeId.lastName}`,
          avatar: leave.employeeId.avatar,
          department: leave.employeeId.departmentId?.name
        },
        leaveType: {
          name: leave.leaveTypeId.name,
          color: leave.leaveTypeId.color
        },
        startDate: leave.startDate,
        endDate: leave.endDate,
        duration: leave.duration,
        daysUntilStart: Math.ceil((leave.startDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
      }));
    } catch (error) {
      logger.error('Error fetching upcoming leaves', LogCategory.DASHBOARD, error);
      throw error;
    }
  }

  /**
   * Transform leave request to dashboard format
   */
  private transformLeaveRequestToDashboard(request: any): DashboardLeaveRequest {
    const employee = request.employeeId;
    const leaveType = request.leaveTypeId;
    const submitter = request.createdBy;
    const approver = request.approvedBy;
    
    const now = new Date();
    const startDate = new Date(request.startDate);
    const daysUntilStart = Math.ceil((startDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    // Calculate urgency based on start date and status
    let urgency: 'low' | 'medium' | 'high' | 'urgent' = 'low';
    if (request.status === 'pending') {
      if (daysUntilStart <= 1) urgency = 'urgent';
      else if (daysUntilStart <= 3) urgency = 'high';
      else if (daysUntilStart <= 7) urgency = 'medium';
    }
    
    // Check if overdue (pending for more than 3 days)
    const submittedAt = new Date(request.createdAt);
    const daysSinceSubmission = Math.ceil((now.getTime() - submittedAt.getTime()) / (1000 * 60 * 60 * 24));
    const isOverdue = request.status === 'pending' && daysSinceSubmission > 3;

    return {
      id: request._id.toString(),
      leaveId: request.leaveId,
      employee: {
        id: employee._id.toString(),
        name: `${employee.firstName} ${employee.lastName}`,
        avatar: employee.avatar,
        initials: `${employee.firstName[0]}${employee.lastName[0]}`,
        department: employee.departmentId?.name,
        position: employee.position
      },
      submittedBy: {
        id: submitter._id.toString(),
        name: `${submitter.firstName} ${submitter.lastName}`,
        avatar: submitter.avatar
      },
      leaveType: {
        id: leaveType._id.toString(),
        name: leaveType.name,
        code: leaveType.code,
        color: leaveType.color,
        isPaid: leaveType.isPaid
      },
      startDate: request.startDate,
      endDate: request.endDate,
      duration: request.duration,
      reason: request.reason,
      status: request.status,
      submittedAt: request.createdAt,
      approvedBy: approver ? {
        id: approver._id.toString(),
        name: `${approver.firstName} ${approver.lastName}`
      } : undefined,
      approvalDate: request.approvalDate,
      rejectionReason: request.rejectionReason,
      attachments: request.attachments,
      notes: request.notes,
      urgency,
      daysUntilStart,
      isOverdue
    };
  }

  // Helper methods for calculations
  private getStartDateForPeriod(period: 'day' | 'week' | 'month' | 'year', now: Date): Date {
    switch (period) {
      case 'day':
        return new Date(now.getFullYear(), now.getMonth(), now.getDate());
      case 'week':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case 'month':
        return new Date(now.getFullYear(), now.getMonth(), 1);
      case 'year':
        return new Date(now.getFullYear(), 0, 1);
      default:
        return new Date(now.getFullYear(), now.getMonth(), 1);
    }
  }

  private isInCurrentMonth(date: Date): boolean {
    const now = new Date();
    return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
  }

  private isInCurrentWeek(date: Date): boolean {
    const now = new Date();
    const weekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    return date >= weekStart;
  }

  private isToday(date: Date): boolean {
    const now = new Date();
    return date.toDateString() === now.toDateString();
  }

  private async calculateLeaveTypeBreakdown(requests: any[]): Promise<Array<{
    leaveType: string;
    leaveTypeId: string;
    color: string;
    count: number;
    totalDays: number;
    percentage: number;
  }>> {
    const breakdown = new Map();
    const total = requests.length;

    requests.forEach(request => {
      const leaveType = request.leaveTypeId;
      const key = leaveType._id.toString();
      
      if (!breakdown.has(key)) {
        breakdown.set(key, {
          leaveType: leaveType.name,
          leaveTypeId: key,
          color: leaveType.color,
          count: 0,
          totalDays: 0,
          percentage: 0
        });
      }
      
      const item = breakdown.get(key);
      item.count++;
      item.totalDays += request.duration;
    });

    // Calculate percentages
    breakdown.forEach(item => {
      item.percentage = total > 0 ? Math.round((item.count / total) * 100) : 0;
    });

    return Array.from(breakdown.values()).sort((a, b) => b.count - a.count);
  }

  private calculateStatusBreakdown(requests: any[]): Array<{
    status: string;
    count: number;
    percentage: number;
  }> {
    const breakdown = new Map();
    const total = requests.length;

    requests.forEach(request => {
      const status = request.status;
      if (!breakdown.has(status)) {
        breakdown.set(status, { status, count: 0, percentage: 0 });
      }
      breakdown.get(status).count++;
    });

    // Calculate percentages
    breakdown.forEach(item => {
      item.percentage = total > 0 ? Math.round((item.count / total) * 100) : 0;
    });

    return Array.from(breakdown.values()).sort((a, b) => b.count - a.count);
  }

  private calculateDepartmentBreakdown(requests: any[]): Array<{
    department: string;
    departmentId: string;
    count: number;
    totalDays: number;
  }> {
    const breakdown = new Map();

    requests.forEach(request => {
      const department = request.employeeId?.departmentId;
      if (!department) return;
      
      const key = department._id.toString();
      const name = department.name;
      
      if (!breakdown.has(key)) {
        breakdown.set(key, {
          department: name,
          departmentId: key,
          count: 0,
          totalDays: 0
        });
      }
      
      const item = breakdown.get(key);
      item.count++;
      item.totalDays += request.duration;
    });

    return Array.from(breakdown.values()).sort((a, b) => b.count - a.count);
  }

  private async calculateLeaveTrends(period: 'day' | 'week' | 'month' | 'year', now: Date): Promise<Array<{
    date: string;
    submitted: number;
    approved: number;
    rejected: number;
  }>> {
    // This would require more complex aggregation
    // For now, return empty array - can be implemented later
    return [];
  }

  private async calculateAverageApprovalTime(period: 'day' | 'week' | 'month' | 'year'): Promise<number> {
    // This would require calculating time between submission and approval
    // For now, return a default value - can be implemented later
    return 48; // 48 hours average
  }

  private async getOverdueRequestsCount(): Promise<number> {
    const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000);
    
    const overdueCount = await Leave.countDocuments({
      status: 'pending',
      createdAt: { $lte: threeDaysAgo }
    });

    return overdueCount;
  }
}

// Export singleton instance
export const leaveDashboardService = new LeaveDashboardService();
