/**
 * Logger utility for consistent logging across the application
 * Provides different log levels and formatting for better debugging
 */

// Log levels
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  FATAL = 'FATAL',  // Added for critical errors
}

// Log categories for better filtering
export enum LogCategory {
  DATABASE = 'DATABASE',
  AUTH = 'AUTH',
  API = 'API',
  MIDDLEWARE = 'MIDDLEWARE',
  GENERAL = 'GENERAL',
  IMPORT = 'IMPORT',
  EXPORT = 'EXPORT',
  REPORTING = 'REPORTING',
  SYSTEM = 'SYSTEM',
  PROCUREMENT = 'PROCUREMENT',
  FINANCE = 'FINANCE',
  HR = 'HR',
  INVENTORY = 'INVENTORY',
  SECURITY = 'SECURITY',  // Added for security-related logs
  FILE = 'FILE',          // Added for file operations
  PROJECT = 'PROJECT',
  ACCOUNTING = 'ACCOUNTING',    // Added for accounting operations
  PAYROLL = 'PAYROLL',    // Added for payroll operations
  BANKING = 'BANKING',    // Added for banking operations
  RECRUITMENT = 'RECRUITMENT',  // Added for recruitment operations
  INTEGRATION = 'INTEGRATION',  // Added for external system integrations
  SYNC = 'SYNC',           // Added for synchronization operations
  AUDIT = 'AUDIT',         // Added for audit logging operations
  ANALYTICS = 'ANALYTICS'  // Added for analytics and AI insights
}

// Interface for log entry
interface LogEntry {
  timestamp: string;
  level: LogLevel;
  category: LogCategory;
  message: string;
  data?: any;
  error?: Error | unknown;  // Allow unknown for better error handling
}

// List of sensitive keys to mask in logs
const SENSITIVE_KEYS = [
  'password',
  'token',
  'secret',
  'apiKey',
  'accessToken',
  'refreshToken',
  'key',
  'credential',
  'pin',
  'ssn',
  'creditCard',
  'cvv'
];

/**
 * Format log entry as string
 * @param entry - Log entry to format
 * @returns Formatted log string
 */
const formatLogEntry = (entry: LogEntry): string => {
  const { timestamp, level, category, message, data, error } = entry;

  // Create a new Set for each call to avoid issues with circular references
  const seen = new Set();

  let logString = `[${timestamp}] [${level}] [${category}] ${message}`;

  if (data) {
    // Safely stringify data, handling circular references
    try {
      const safeData = JSON.stringify(data, (key, value) => {
        // Handle circular references and functions
        if (typeof value === 'function') return '[Function]';
        if (typeof value === 'symbol') return value.toString();
        if (key && typeof value === 'object' && value !== null) {
          if (seen.has(value)) return '[Circular]';
          seen.add(value);
        }

        // Mask sensitive data (case insensitive check)
        const lowerKey = typeof key === 'string' ? key.toLowerCase() : key;
        if (
          typeof lowerKey === 'string' &&
          SENSITIVE_KEYS.some(k => lowerKey.includes(k.toLowerCase()))
        ) {
          return '********';
        }

        return value;
      }, 2);

      logString += `\nData: ${safeData}`;
    } catch (err: any) {
      logString += `\nData: [Could not stringify data: ${err?.message || 'Unknown error'}]`;
    }
  }

  if (error) {
    if (error instanceof Error) {
      logString += `\nError: ${error.message}`;
      if (error.stack) {
        logString += `\nStack: ${error.stack}`;
      }
    } else {
      // Handle non-Error objects
      try {
        logString += `\nError: ${JSON.stringify(error)}`;
      } catch (err: any) {
        logString += `\nError: [Could not stringify error: ${err?.message || 'Unknown error'}]`;
      }
    }
  }

  return logString;
};

/**
 * Get the current log level from environment variables
 * @returns Current log level
 */
const getCurrentLogLevel = (): LogLevel => {
  const envLogLevel = process.env.LOG_LEVEL?.toUpperCase();
  if (envLogLevel && Object.values(LogLevel).includes(envLogLevel as LogLevel)) {
    return envLogLevel as LogLevel;
  }

  // Default log levels based on environment
  return process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG;
};

/**
 * Check if a log level should be logged based on the current log level
 * @param level - Log level to check
 * @returns True if the log level should be logged
 */
const shouldLog = (level: LogLevel): boolean => {
  const currentLevel = getCurrentLogLevel();
  const levels = Object.values(LogLevel);
  const currentLevelIndex = levels.indexOf(currentLevel);
  const logLevelIndex = levels.indexOf(level);

  return logLevelIndex >= currentLevelIndex;
};

/**
 * Get the current timestamp in ISO format with milliseconds
 * @returns Timestamp string
 */
const getTimestamp = (): string => {
  return new Date().toISOString();
};

/**
 * Main logger object with methods for different log levels
 */
export const logger = {
  /**
   * Log a debug message
   * @param message - Log message
   * @param category - Log category
   * @param data - Optional data to log
   * @returns Log entry
   */
  debug: (message: string, category: LogCategory = LogCategory.GENERAL, data?: any): LogEntry => {
    const entry: LogEntry = {
      timestamp: getTimestamp(),
      level: LogLevel.DEBUG,
      category,
      message,
      data,
    };

    if (shouldLog(LogLevel.DEBUG)) {
      console.debug(formatLogEntry(entry));
    }

    return entry;
  },

  /**
   * Log an info message
   * @param message - Log message
   * @param category - Log category
   * @param data - Optional data to log
   * @returns Log entry
   */
  info: (message: string, category: LogCategory = LogCategory.GENERAL, data?: any): LogEntry => {
    const entry: LogEntry = {
      timestamp: getTimestamp(),
      level: LogLevel.INFO,
      category,
      message,
      data,
    };

    if (shouldLog(LogLevel.INFO)) {
      console.info(formatLogEntry(entry));
    }

    return entry;
  },

  /**
   * Log a warning message
   * @param message - Log message
   * @param category - Log category
   * @param data - Optional data to log
   * @returns Log entry
   */
  warn: (message: string, category: LogCategory = LogCategory.GENERAL, data?: any): LogEntry => {
    const entry: LogEntry = {
      timestamp: getTimestamp(),
      level: LogLevel.WARN,
      category,
      message,
      data,
    };

    if (shouldLog(LogLevel.WARN)) {
      console.warn(formatLogEntry(entry));
    }

    return entry;
  },

  /**
   * Log an error message
   * @param message - Log message
   * @param category - Log category
   * @param error - Optional error object
   * @param data - Optional data to log
   * @returns Log entry
   */
  error: (
    message: string,
    category: LogCategory = LogCategory.GENERAL,
    error?: Error | unknown,
    data?: any
  ): LogEntry => {
    const entry: LogEntry = {
      timestamp: getTimestamp(),
      level: LogLevel.ERROR,
      category,
      message,
      data,
      error,
    };

    if (shouldLog(LogLevel.ERROR)) {
      console.error(formatLogEntry(entry));
    }

    return entry;
  },

  /**
   * Log a fatal error message
   * @param message - Log message
   * @param category - Log category
   * @param error - Optional error object
   * @param data - Optional data to log
   * @returns Log entry
   */
  fatal: (
    message: string,
    category: LogCategory = LogCategory.GENERAL,
    error?: Error | unknown,
    data?: any
  ): LogEntry => {
    const entry: LogEntry = {
      timestamp: getTimestamp(),
      level: LogLevel.FATAL,
      category,
      message,
      data,
      error,
    };

    // Always log fatal errors regardless of log level
    console.error(formatLogEntry(entry));

    return entry;
  }
};

export default logger;
