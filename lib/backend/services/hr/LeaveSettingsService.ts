// import { CrudService } from '../base/CrudService'; // Commented out - not available in TCM
import LeaveSettings, { ILeaveSettings } from '@/models/leave/LeaveSettings';
import logger, { LogCategory } from '@/lib/logger';
import { connectToDatabase } from '@/lib/database';
import mongoose from 'mongoose';

/**
 * Service for managing leave settings
 */
export class LeaveSettingsService extends CrudService<ILeaveSettings> {
  constructor() {
    super(LeaveSettings, 'LeaveSettings');
  }

  /**
   * Get leave settings for organization or global settings
   * @param organizationId - Optional organization ID
   * @returns Leave settings
   */
  async getSettings(organizationId?: string): Promise<ILeaveSettings | null> {
    try {
      await connectToDatabase();
      
      const filter: any = {};
      if (organizationId) {
        filter.organizationId = new mongoose.Types.ObjectId(organizationId);
      } else {
        filter.organizationId = { $exists: false };
      }

      let settings = await LeaveSettings.findOne(filter);
      
      // If no settings found, create default settings
      if (!settings) {
        logger.info('No leave settings found, creating default settings', LogCategory.HR, { organizationId });
        settings = await this.createDefaultSettings(organizationId);
      }

      return settings;
    } catch (error) {
      logger.error('Error getting leave settings', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Update leave settings
   * @param settingsData - Settings data to update
   * @param userId - User ID making the update
   * @param organizationId - Optional organization ID
   * @returns Updated settings
   */
  async updateSettings(
    settingsData: Partial<ILeaveSettings>,
    userId: string,
    organizationId?: string
  ): Promise<ILeaveSettings> {
    try {
      await connectToDatabase();
      
      const filter: any = {};
      if (organizationId) {
        filter.organizationId = new mongoose.Types.ObjectId(organizationId);
      } else {
        filter.organizationId = { $exists: false };
      }

      // Prepare update data
      const updateData = {
        ...settingsData,
        updatedBy: new mongoose.Types.ObjectId(userId),
      };

      // Remove system fields that shouldn't be updated directly
      delete updateData.createdBy;
      delete updateData.createdAt;
      delete updateData.updatedAt;
      delete updateData._id;

      let settings = await LeaveSettings.findOneAndUpdate(
        filter,
        updateData,
        { new: true, runValidators: true }
      );

      // If no settings found, create new ones
      if (!settings) {
        logger.info('No existing settings found, creating new settings', LogCategory.HR, { organizationId });
        settings = await LeaveSettings.create({
          ...updateData,
          createdBy: new mongoose.Types.ObjectId(userId),
          ...(organizationId && { organizationId: new mongoose.Types.ObjectId(organizationId) }),
        });
      }

      logger.info('Leave settings updated successfully', LogCategory.HR, {
        settingsId: settings._id,
        userId,
        organizationId
      });

      return settings;
    } catch (error) {
      logger.error('Error updating leave settings', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Create default leave settings
   * @param organizationId - Optional organization ID
   * @returns Default settings
   */
  private async createDefaultSettings(organizationId?: string): Promise<ILeaveSettings> {
    try {
      // Find a system admin to use as creator
      const User = mongoose.models.User;
      const systemAdmin = await User.findOne({
        role: { $in: ['super_admin', 'system_admin'] }
      });

      if (!systemAdmin) {
        throw new Error('No system admin found to create default settings');
      }

      const defaultSettings = {
        // General Settings
        carryOverEnabled: true,
        maxCarryOverDays: 5,
        minNoticeDays: 3,
        autoApproveEnabled: false,
        approvalWorkflow: 'single' as const,
        
        // Leave Year Settings
        defaultLeaveYear: 'calendar' as const,
        fiscalYearStart: '01-04',
        
        // Accrual Settings
        leaveAccrualFrequency: 'monthly' as const,
        proRataCalculationEnabled: true,
        holidayExclusionEnabled: true,
        weekendExclusionEnabled: true,
        halfDayLeaveEnabled: true,
        hourlyLeaveEnabled: false,
        
        // Encashment Settings
        leaveEncashmentEnabled: false,
        leaveEncashmentLimit: 10,
        leaveEncashmentRate: 100,
        
        // Request Management
        leaveRequestCancellationEnabled: true,
        maxCancellationDays: 2,
        
        // Balance & Warnings
        leaveBalanceDisplayEnabled: true,
        leaveBalanceWarningThreshold: 5,
        leaveBalanceWarningEnabled: true,
        
        // Documentation
        leaveDocumentationRequired: false,
        leaveDocumentationDays: 3,
        
        // Notification Settings
        emailNotificationsEnabled: true,
        calendarIntegrationEnabled: true,
        
        // System Fields
        createdBy: systemAdmin._id,
        ...(organizationId && { organizationId: new mongoose.Types.ObjectId(organizationId) }),
      };

      const settings = await LeaveSettings.create(defaultSettings);
      
      logger.info('Default leave settings created', LogCategory.HR, {
        settingsId: settings._id,
        organizationId
      });

      return settings;
    } catch (error) {
      logger.error('Error creating default leave settings', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Reset settings to defaults
   * @param userId - User ID making the reset
   * @param organizationId - Optional organization ID
   * @returns Reset settings
   */
  async resetToDefaults(userId: string, organizationId?: string): Promise<ILeaveSettings> {
    try {
      await connectToDatabase();
      
      const filter: any = {};
      if (organizationId) {
        filter.organizationId = new mongoose.Types.ObjectId(organizationId);
      } else {
        filter.organizationId = { $exists: false };
      }

      // Delete existing settings
      await LeaveSettings.deleteOne(filter);
      
      // Create new default settings
      const settings = await this.createDefaultSettings(organizationId);
      
      logger.info('Leave settings reset to defaults', LogCategory.HR, {
        settingsId: settings._id,
        userId,
        organizationId
      });

      return settings;
    } catch (error) {
      logger.error('Error resetting leave settings to defaults', LogCategory.HR, error);
      throw error;
    }
  }

  /**
   * Validate settings data
   * @param settingsData - Settings data to validate
   * @returns Validation result
   */
  validateSettings(settingsData: Partial<ILeaveSettings>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate fiscal year start format
    if (settingsData.fiscalYearStart && !/^\d{2}-\d{2}$/.test(settingsData.fiscalYearStart)) {
      errors.push('Fiscal year start must be in DD-MM format');
    }

    // Validate numeric fields
    if (settingsData.maxCarryOverDays !== undefined && settingsData.maxCarryOverDays < 0) {
      errors.push('Maximum carry-over days must be non-negative');
    }

    if (settingsData.minNoticeDays !== undefined && settingsData.minNoticeDays < 0) {
      errors.push('Minimum notice days must be non-negative');
    }

    if (settingsData.leaveEncashmentRate !== undefined && 
        (settingsData.leaveEncashmentRate < 0 || settingsData.leaveEncashmentRate > 200)) {
      errors.push('Leave encashment rate must be between 0 and 200 percent');
    }

    if (settingsData.leaveBalanceWarningThreshold !== undefined && settingsData.leaveBalanceWarningThreshold < 0) {
      errors.push('Leave balance warning threshold must be non-negative');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Export singleton instance
export const leaveSettingsService = new LeaveSettingsService();
