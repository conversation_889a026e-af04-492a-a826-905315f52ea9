import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import logger, { LogCategory } from '@/lib/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveEncashmentService } from '@/services/leave/LeaveEncashmentService';
import LeaveEncashment from '@/models/leave/LeaveEncashment';

export const runtime = 'nodejs';

/**
 * GET /api/leave/encashments
 * Get leave encashment requests
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');
    const year = searchParams.get('year');
    const employeeId = searchParams.get('employeeId');
    // Build query
    const query: any = {};
    // Check permissions - users can only see their own encashments unless they have HR permissions
    const hasHRPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);
    if (!hasHRPermission) {
      query.employeeId = user.id;
    } else if (employeeId) {
      query.employeeId = employeeId;
    }
    if (status) {
      query.status = status;
    }
    if (year) {
      query.year = parseInt(year);
    }
    // Get encashments with pagination
    const skip = (page - 1) * limit;
    const encashments = await LeaveEncashment.find(query)
      .populate('employeeId', 'firstName lastName position')
      .populate('leaveTypeId', 'name code color')
      .populate('approvedBy', 'firstName lastName')
      .populate('createdBy', 'firstName lastName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);
    const total = await LeaveEncashment.countDocuments(query);
    const totalPages = Math.ceil(total / limit);
    return NextResponse.json({
      data: encashments,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    });
  } catch (error: unknown) {
    logger.error('Error fetching leave encashments', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while fetching encashments' 
      },
      { status: 500 }
    );
  }
}
/**
 * POST /api/leave/encashments
 * Create a new leave encashment request
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get request body
    const body = await req.json();
    // Validate required fields
    if (!body.employeeId || !body.leaveTypeId || !body.year || !body.daysToEncash) {
      return NextResponse.json(
        { error: 'Missing required fields: employeeId, leaveTypeId, year, daysToEncash' },
        { status: 400 }
      );
    }
    // Check permissions - users can only create encashments for themselves unless they have HR permissions
    const hasHRPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);
    if (!hasHRPermission && body.employeeId !== user.id) {
      return NextResponse.json(
        { error: 'Forbidden - You can only create encashment requests for yourself' },
        { status: 403 }
      );
    }
    // Validate days to encash
    if (body.daysToEncash <= 0) {
      return NextResponse.json(
        { error: 'Days to encash must be greater than 0' },
        { status: 400 }
      );
    }
    // Create encashment request
    const encashment = await leaveEncashmentService.createEncashmentRequest(body, user.id);
    // Populate the response
    const populatedEncashment = await LeaveEncashment.findById(encashment._id)
      .populate('employeeId', 'firstName lastName position')
      .populate('leaveTypeId', 'name code color')
      .populate('createdBy', 'firstName lastName');
    return NextResponse.json({
      success: true,
      message: 'Leave encashment request created successfully',
      data: populatedEncashment
    });
  } catch (error: unknown) {
    logger.error('Error creating leave encashment request', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while creating encashment request' 
      },
      { status: 500 }
    );
  }
}