import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { connectToDatabase } from '@/lib/database';
import logger, { LogCategory } from '@/lib/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveReportingService } from '@/services/leave/LeaveReportingService';

export const runtime = 'nodejs';

/**
 * GET /api/leave/reports/trends
 * Generate trend analysis report
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions to view reports' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const period = searchParams.get('period') as 'monthly' | 'quarterly' | 'yearly';
    const periodsBackParam = searchParams.get('periodsBack');
    const format = searchParams.get('format'); // 'json' or 'csv'
    // Validate period
    if (!period || !['monthly', 'quarterly', 'yearly'].includes(period)) {
      return NextResponse.json(
        { error: 'Invalid period. Must be monthly, quarterly, or yearly' },
        { status: 400 }
      );
    }
    // Validate periodsBack
    let periodsBack = 12; // Default
    if (periodsBackParam) {
      periodsBack = parseInt(periodsBackParam);
      if (isNaN(periodsBack) || periodsBack < 1 || periodsBack > 60) {
        return NextResponse.json(
          { error: 'Invalid periodsBack. Must be between 1 and 60' },
          { status: 400 }
        );
      }
    }
    // Generate report
    const report = await leaveReportingService.generateTrendAnalysis(period, periodsBack);
    // Handle CSV export
    if (format === 'csv') {
      const csvContent = await leaveReportingService.exportToCSV(report, 'trend');
      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="trend-analysis-report-${period}-${periodsBack}periods.csv"`
        }
      });
    }
    return NextResponse.json({
      success: true,
      data: report,
      metadata: {
        reportType: 'trends',
        period,
        periodsBack,
        generatedAt: new Date().toISOString(),
        generatedBy: user.id
      }
    });
  } catch (error: unknown) {
    logger.error('Error generating trend analysis report', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while generating the report' 
      },
      { status: 500 }
    );
  }
}