import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { connectToDatabase } from '@/lib/database';
import logger, { LogCategory } from '@/lib/logger';
import { hasRequiredPermissions } from '@/lib/permissions';
import { UserRole } from '@/types/user-roles';
import Employee from '@/models/Employee';
import LeaveBalance from '@/models/leave/LeaveBalance';
import LeaveType from '@/models/leave/LeaveType';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

/**
 * GET /api/leave/balances/departments
 * Get leave balance summaries by department
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions
    const hasHRPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD,
      UserRole.TEAM_LEADER
    ]);
    if (!hasHRPermission) {
      return NextResponse.json(
        { error: 'Forbidden: You do not have permission to view department leave summaries' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString());
    // Get department summaries using aggregation
    const departmentSummaries = await Employee.aggregate([
      {
        $match: {
          employmentStatus: 'active',
          department: { $exists: true, $ne: null, $ne: '' }
        }
      },
      {
        $group: {
          _id: '$department',
          employeeCount: { $sum: 1 },
          employees: {
            $push: {
              _id: '$_id',
              firstName: '$firstName',
              lastName: '$lastName',
              employeeId: '$employeeId',
              position: '$position'
            }
          }
        }
      },
      {
        $sort: { _id: 1 }
      }
    ]);
    // Get all leave types for reference
    const leaveTypes = await LeaveType.find({ isActive: true }).select('_id name code color');
    // Get leave balances for all active employees
    const allEmployeeIds = departmentSummaries.flatMap(dept => 
      dept.employees.map((emp: any) => emp._id)
    );
    const leaveBalances = await LeaveBalance.find({
      employeeId: { $in: allEmployeeIds },
      year
    }).populate('leaveTypeId', 'name code color');
    // Group balances by department and leave type
    const balancesByDepartment = departmentSummaries.map(dept => {
      const deptEmployeeIds = dept.employees.map((emp: any) => emp._id.toString());
      const deptBalances = leaveBalances.filter(balance => 
        deptEmployeeIds.includes(balance.employeeId.toString())
      );
      // Calculate totals by leave type
      const leaveTypeSummaries = leaveTypes.map(leaveType => {
        const typeBalances = deptBalances.filter(balance => 
          balance.leaveTypeId._id.toString() === leaveType._id.toString()
        );
        const totalDays = typeBalances.reduce((sum, b) => sum + b.totalDays, 0);
        const usedDays = typeBalances.reduce((sum, b) => sum + b.usedDays, 0);
        const pendingDays = typeBalances.reduce((sum, b) => sum + b.pendingDays, 0);
        const remainingDays = typeBalances.reduce((sum, b) => sum + b.remainingDays, 0);
        return {
          leaveType: {
            _id: leaveType._id,
            name: leaveType.name,
            code: leaveType.code,
            color: leaveType.color
          },
          totalDays,
          usedDays,
          pendingDays,
          remainingDays,
          utilizationPercentage: totalDays > 0 ? Math.round((usedDays / totalDays) * 100) : 0,
          employeesWithBalance: typeBalances.length
        };
      });
      // Calculate overall department totals
      const overallTotalDays = deptBalances.reduce((sum, b) => sum + b.totalDays, 0);
      const overallUsedDays = deptBalances.reduce((sum, b) => sum + b.usedDays, 0);
      const overallPendingDays = deptBalances.reduce((sum, b) => sum + b.pendingDays, 0);
      const overallRemainingDays = deptBalances.reduce((sum, b) => sum + b.remainingDays, 0);
      return {
        department: dept._id,
        employeeCount: dept.employeeCount,
        leaveTypeSummaries,
        overallSummary: {
          totalDays: overallTotalDays,
          usedDays: overallUsedDays,
          pendingDays: overallPendingDays,
          remainingDays: overallRemainingDays,
          utilizationPercentage: overallTotalDays > 0 ? Math.round((overallUsedDays / overallTotalDays) * 100) : 0
        }
      };
    });
    // Calculate grand totals across all departments
    const grandTotals = {
      totalEmployees: departmentSummaries.reduce((sum, dept) => sum + dept.employeeCount, 0),
      totalDays: balancesByDepartment.reduce((sum, dept) => sum + dept.overallSummary.totalDays, 0),
      usedDays: balancesByDepartment.reduce((sum, dept) => sum + dept.overallSummary.usedDays, 0),
      pendingDays: balancesByDepartment.reduce((sum, dept) => sum + dept.overallSummary.pendingDays, 0),
      remainingDays: balancesByDepartment.reduce((sum, dept) => sum + dept.overallSummary.remainingDays, 0)
    };
    grandTotals.utilizationPercentage = grandTotals.totalDays > 0 ? 
      Math.round((grandTotals.usedDays / grandTotals.totalDays) * 100) : 0;
    return NextResponse.json({
      data: balancesByDepartment,
      grandTotals,
      year,
      leaveTypes: leaveTypes.map(lt => ({
        _id: lt._id,
        name: lt.name,
        code: lt.code,
        color: lt.color
      }))
    });
  } catch (error: unknown) {
    logger.error('Error getting department leave summaries', LogCategory.HR, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}