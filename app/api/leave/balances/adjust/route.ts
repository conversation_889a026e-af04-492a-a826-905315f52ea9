import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { connectToDatabase } from '@/lib/database';
import logger, { LogCategory } from '@/lib/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveService } from '@/services/leave/LeaveService';
import LeaveBalance from '@/models/leave/LeaveBalance';
import LeaveType from '@/models/leave/LeaveType';
import Employee from '@/models/Employee';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

interface BalanceAdjustmentPayload {
  employeeId: string;
  leaveTypeId: string;
  year: number;
  adjustmentType: 'add' | 'subtract' | 'set';
  days: number;
  reason: string;
}
/**
 * POST /api/leave/balances/adjust
 * Adjust leave balance for an employee
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions - only HR can adjust balances
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions to adjust leave balances' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get request body
    const body: BalanceAdjustmentPayload = await req.json();
    // Validate required fields
    if (!body.employeeId || !body.leaveTypeId || !body.year || !body.adjustmentType || body.days === undefined || !body.reason) {
      return NextResponse.json(
        { error: 'Missing required fields: employeeId, leaveTypeId, year, adjustmentType, days, reason' },
        { status: 400 }
      );
    }
    // Validate IDs
    if (!mongoose.Types.ObjectId.isValid(body.employeeId)) {
      return NextResponse.json(
        { error: 'Invalid employee ID format' },
        { status: 400 }
      );
    }
    if (!mongoose.Types.ObjectId.isValid(body.leaveTypeId)) {
      return NextResponse.json(
        { error: 'Invalid leave type ID format' },
        { status: 400 }
      );
    }
    // Validate adjustment type
    if (!['add', 'subtract', 'set'].includes(body.adjustmentType)) {
      return NextResponse.json(
        { error: 'Invalid adjustment type. Must be add, subtract, or set' },
        { status: 400 }
      );
    }
    // Validate days
    if (body.days < 0) {
      return NextResponse.json(
        { error: 'Days must be a positive number' },
        { status: 400 }
      );
    }
    // Validate year
    const currentYear = new Date().getFullYear();
    if (body.year < currentYear - 5 || body.year > currentYear + 5) {
      return NextResponse.json(
        { error: 'Year must be within 5 years of current year' },
        { status: 400 }
      );
    }
    // Check if employee exists
    const employee = await Employee.findById(body.employeeId);
    if (!employee) {
      return NextResponse.json(
        { error: 'Employee not found' },
        { status: 404 }
      );
    }
    // Check if leave type exists
    const leaveType = await LeaveType.findById(body.leaveTypeId);
    if (!leaveType) {
      return NextResponse.json(
        { error: 'Leave type not found' },
        { status: 404 }
      );
    }
    // Get or create leave balance
    let leaveBalance = await leaveService.getOrCreateLeaveBalance(
      body.employeeId,
      body.leaveTypeId,
      body.year,
      user.id
    );
    // Store original values for logging
    const originalTotalDays = leaveBalance.totalDays;
    const originalRemainingDays = leaveBalance.remainingDays;
    // Apply adjustment
    switch (body.adjustmentType) {
      case 'add':
        leaveBalance.totalDays += body.days;
        break;
      case 'subtract':
        leaveBalance.totalDays = Math.max(0, leaveBalance.totalDays - body.days);
        break;
      case 'set':
        leaveBalance.totalDays = body.days;
        break;
    }
    // Recalculate remaining days
    leaveBalance.remainingDays = leaveBalance.totalDays - leaveBalance.usedDays - leaveBalance.pendingDays;
    // Ensure remaining days is not negative
    if (leaveBalance.remainingDays < 0) {
      return NextResponse.json(
        { 
          error: `Adjustment would result in negative remaining days. Current used: ${leaveBalance.usedDays}, pending: ${leaveBalance.pendingDays}, new total: ${leaveBalance.totalDays}` 
        },
        { status: 400 }
      );
    }
    // Update balance with adjustment details
    leaveBalance.notes = `${leaveBalance.notes ? leaveBalance.notes + '\n' : ''}Balance adjustment (${body.adjustmentType} ${body.days} days): ${body.reason}`;
    leaveBalance.updatedBy = new mongoose.Types.ObjectId(user.id);
    // Save the updated balance
    await leaveBalance.save();
    // Log the adjustment
    logger.info('Leave balance adjusted', LogCategory.HR, {
      employeeId: body.employeeId,
      leaveTypeId: body.leaveTypeId,
      year: body.year,
      adjustmentType: body.adjustmentType,
      days: body.days,
      reason: body.reason,
      originalTotalDays,
      newTotalDays: leaveBalance.totalDays,
      originalRemainingDays,
      newRemainingDays: leaveBalance.remainingDays,
      adjustedBy: user.id
    });
    // Populate the response
    const populatedBalance = await LeaveBalance.findById(leaveBalance._id)
      .populate('leaveTypeId', 'name code color')
      .populate('employeeId', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName');
    return NextResponse.json({
      success: true,
      message: 'Leave balance adjusted successfully',
      data: populatedBalance,
      adjustment: {
        type: body.adjustmentType,
        days: body.days,
        reason: body.reason,
        previousTotal: originalTotalDays,
        newTotal: leaveBalance.totalDays,
        previousRemaining: originalRemainingDays,
        newRemaining: leaveBalance.remainingDays
      }
    });
  } catch (error: unknown) {
    logger.error('Error adjusting leave balance', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while adjusting leave balance' 
      },
      { status: 500 }
    );
  }
}