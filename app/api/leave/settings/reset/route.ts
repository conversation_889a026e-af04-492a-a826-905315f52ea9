import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveSettingsService } from '@/lib/backend/services/hr/LeaveSettingsService';
import logger, { LogCategory } from '@/lib/logger';

export const runtime = 'nodejs';

/**
 * POST /api/leave/settings/reset
 * Reset leave settings to defaults
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only super admin and system admin can reset settings
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Only system administrators can reset settings' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await req.json().catch(() => ({}));
    const { organizationId } = body;

    logger.info('Resetting leave settings to defaults', LogCategory.HR, {
      userId: user.id,
      userRole: user.role,
      organizationId
    });

    // Reset settings
    const resetSettings = await leaveSettingsService.resetToDefaults(
      user.id,
      organizationId
    );

    logger.info('Leave settings reset to defaults successfully', LogCategory.HR, {
      settingsId: resetSettings._id,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      data: resetSettings,
      message: 'Leave settings reset to defaults successfully'
    });

  } catch (error) {
    logger.error('Error resetting leave settings', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: 'Failed to reset leave settings',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
