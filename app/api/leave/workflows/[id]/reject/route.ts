import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { connectToDatabase } from '@/lib/database';
import logger, { LogCategory } from '@/lib/logger';
import { leaveApprovalWorkflowService } from '@/services/leave/LeaveApprovalWorkflowService';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

interface RejectStepPayload {
  stepNumber: number;
  rejectionReason: string;
}
/**
 * POST /api/leave/workflows/[id]/reject
 * Reject a workflow step
 */
export async function POST(
  req: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get the workflow ID from params
    const { id } = await params;
    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid workflow ID format' },
        { status: 400 }
      );
    }
    // Get request body
    const body: RejectStepPayload = await req.json();
    // Validate required fields
    if (!body.stepNumber || !body.rejectionReason) {
      return NextResponse.json(
        { error: 'Missing required fields: stepNumber, rejectionReason' },
        { status: 400 }
      );
    }
    // Reject the workflow step
    const result = await leaveApprovalWorkflowService.rejectStep(
      id,
      body.stepNumber,
      user.id,
      body.rejectionReason
    );
    return NextResponse.json({
      success: true,
      message: result.message,
      data: {
        workflowCompleted: result.workflowCompleted,
        finalStatus: result.finalStatus
      }
    });
  } catch (error: unknown) {
    logger.error('Error rejecting workflow step', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while rejecting workflow step' 
      },
      { status: 500 }
    );
  }
}