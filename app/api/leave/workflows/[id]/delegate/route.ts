import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import logger, { LogCategory } from '@/lib/logger';
import { leaveApprovalWorkflowService } from '@/services/leave/LeaveApprovalWorkflowService';
import mongoose from 'mongoose';

export const runtime = 'nodejs';

interface DelegateStepPayload {
  stepNumber: number;
  delegateToId: string;
  delegationReason: string;
}
/**
 * POST /api/leave/workflows/[id]/delegate
 * Delegate a workflow step to another user
 */
export async function POST(
  req: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get the workflow ID from params
    const { id } = await params;
    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid workflow ID format' },
        { status: 400 }
      );
    }
    // Get request body
    const body: DelegateStepPayload = await req.json();
    // Validate required fields
    if (!body.stepNumber || !body.delegateToId || !body.delegationReason) {
      return NextResponse.json(
        { error: 'Missing required fields: stepNumber, delegateToId, delegationReason' },
        { status: 400 }
      );
    }
    // Validate delegate user ID format
    if (!mongoose.Types.ObjectId.isValid(body.delegateToId)) {
      return NextResponse.json(
        { error: 'Invalid delegate user ID format' },
        { status: 400 }
      );
    }
    // Delegate the workflow step
    const result = await leaveApprovalWorkflowService.delegateStep(
      id,
      body.stepNumber,
      user.id,
      body.delegateToId,
      body.delegationReason
    );
    return NextResponse.json({
      success: true,
      message: result.message,
      data: {
        nextApprovers: result.nextApprovers,
        workflowCompleted: result.workflowCompleted
      }
    });
  } catch (error: unknown) {
    logger.error('Error delegating workflow step', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while delegating workflow step' 
      },
      { status: 500 }
    );
  }
}