import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import logger, { LogCategory } from '@/lib/logger';
import { hasRequiredPermissions } from '@/lib/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveService } from '@/services/leave/LeaveService';
import Leave from '@/models/leave/Leave';
import mongoose from 'mongoose';
import { 
  UpdateLeaveRequestPayload, 
  LeaveRequestResponse 
} from '@/types/leave-request';

export const runtime = 'nodejs';

/**
 * GET /api/leave/requests/[id]
 * Get a specific leave request
 */
export async function GET(
  req: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get the ID from params
    const { id } = await params;
    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid leave request ID format' },
        { status: 400 }
      );
    }
    // Get leave request
    const leaveRequest = await Leave.findById(id)
      .populate('employeeId', 'firstName lastName position avatar')
      .populate('leaveTypeId', 'name code color')
      .populate('approvedBy', 'firstName lastName')
      .populate('createdBy', 'firstName lastName')
      .lean();
    if (!leaveRequest) {
      return NextResponse.json(
        { error: 'Leave request not found' },
        { status: 404 }
      );
    }
    // Check permissions - user can view their own requests or if they have HR permissions
    const isOwnRequest = leaveRequest.employeeId._id.toString() === user.id;
    const hasHRPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST,
      UserRole.DEPARTMENT_HEAD
    ]);
    if (!isOwnRequest && !hasHRPermission) {
      return NextResponse.json(
        { error: 'Forbidden - You can only view your own leave requests' },
        { status: 403 }
      );
    }
    return NextResponse.json({
      success: true,
      data: leaveRequest
    });
  } catch (error: unknown) {
    logger.error('Error getting leave request', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while fetching leave request' 
      },
      { status: 500 }
    );
  }
}
/**
 * PUT /api/leave/requests/[id]
 * Update a leave request (only if pending)
 */
export async function PUT(
  req: NextRequest, 
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get the ID from params
    const { id } = await params;
    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid leave request ID format' },
        { status: 400 }
      );
    }
    // Get request body
    const body: UpdateLeaveRequestPayload = await req.json();
    // Get existing leave request
    const existingRequest = await Leave.findById(id);
    if (!existingRequest) {
      return NextResponse.json(
        { error: 'Leave request not found' },
        { status: 404 }
      );
    }
    // Check if user owns this request or has HR permissions
    const isOwnRequest = existingRequest.employeeId.toString() === user.id;
    const hasHRPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);
    if (!isOwnRequest && !hasHRPermission) {
      return NextResponse.json(
        { error: 'Forbidden - You can only update your own leave requests' },
        { status: 403 }
      );
    }
    // Check if request is still pending
    if (existingRequest.status !== 'pending') {
      return NextResponse.json(
        { error: `Cannot update leave request with status: ${existingRequest.status}` },
        { status: 400 }
      );
    }
    // Update the leave request
    const updatedRequest = await leaveService.updateLeaveRequest(id, body, user.id);
    return NextResponse.json({
      success: true,
      message: 'Leave request updated successfully',
      data: updatedRequest
    } as LeaveRequestResponse);
  } catch (error: unknown) {
    logger.error('Error updating leave request', LogCategory.HR, error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'An error occurred while updating leave request'
      },
      { status: 500 }
    );
  }
}
/**
 * DELETE /api/leave/requests/[id]
 * Cancel a leave request (only if pending)
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Connect to database
    await connectToDatabase();
    // Get the ID from params
    const { id } = await params;
    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid leave request ID format' },
        { status: 400 }
      );
    }
    // Get existing leave request
    const existingRequest = await Leave.findById(id);
    if (!existingRequest) {
      return NextResponse.json(
        { error: 'Leave request not found' },
        { status: 404 }
      );
    }
    // Check if user owns this request or has HR permissions
    const isOwnRequest = existingRequest.employeeId.toString() === user.id;
    const hasHRPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);
    if (!isOwnRequest && !hasHRPermission) {
      return NextResponse.json(
        { error: 'Forbidden - You can only cancel your own leave requests' },
        { status: 403 }
      );
    }
    // Check if request can be cancelled
    if (existingRequest.status === 'cancelled') {
      return NextResponse.json(
        { error: 'Leave request is already cancelled' },
        { status: 400 }
      );
    }
    if (existingRequest.status === 'approved') {
      // Only allow cancellation of approved requests if they haven't started yet
      const today = new Date();
      const startDate = new Date(existingRequest.startDate);
      if (startDate <= today) {
        return NextResponse.json(
          { error: 'Cannot cancel approved leave request that has already started' },
          { status: 400 }
        );
      }
    }
    // Cancel the leave request
    const cancelledRequest = await leaveService.updateLeaveRequestStatus(
      id,
      'cancelled',
      user.id
    );
    return NextResponse.json({
      success: true,
      message: 'Leave request cancelled successfully',
      data: cancelledRequest
    } as LeaveRequestResponse);
  } catch (error: unknown) {
    logger.error('Error cancelling leave request', LogCategory.HR, error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'An error occurred while cancelling leave request'
      },
      { status: 500 }
    );
  }
}