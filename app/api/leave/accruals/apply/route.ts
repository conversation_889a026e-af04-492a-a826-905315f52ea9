import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { connectToDatabase } from '@/lib/database';
import logger, { LogCategory } from '@/lib/logger';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import { leaveAccrualService } from '@/services/leave/LeaveAccrualService';

export const runtime = 'nodejs';

interface ApplyAccrualsPayload {
  accrualIds: string[];
}
/**
 * POST /api/leave/accruals/apply
 * Apply accruals to leave balances
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Check permissions - only <PERSON><PERSON> can apply accruals
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.HR_DIRECTOR,
      UserRole.HR_MANAGER,
      UserRole.HR_SPECIALIST
    ]);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden - Insufficient permissions to apply accruals' },
        { status: 403 }
      );
    }
    // Connect to database
    await connectToDatabase();
    // Get request body
    const body: ApplyAccrualsPayload = await req.json();
    // Validate required fields
    if (!body.accrualIds || !Array.isArray(body.accrualIds) || body.accrualIds.length === 0) {
      return NextResponse.json(
        { error: 'Missing or invalid accrualIds array' },
        { status: 400 }
      );
    }
    // Apply accruals to balances
    const appliedCount = await leaveAccrualService.applyAccrualsToBalances(body.accrualIds, user.id);
    return NextResponse.json({
      success: true,
      message: `${appliedCount} accruals applied to leave balances successfully`,
      data: {
        appliedCount,
        totalRequested: body.accrualIds.length
      }
    });
  } catch (error: unknown) {
    logger.error('Error applying leave accruals', LogCategory.HR, error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'An error occurred while applying accruals' 
      },
      { status: 500 }
    );
  }
}